2025-05-23 18:44:44,667 - INFO - logger.py:39 - Logging configured
2025-05-23 18:44:44,667 - INFO - main.py:25 - Starting TikTok Automation Application
2025-05-23 18:44:44,667 - INFO - main.py:30 - Creating default configuration
2025-05-23 18:44:44,669 - INFO - config.py:81 - Created default configuration at S:\Automatisation\tiktok_automatisation\config.json
2025-05-23 18:44:44,677 - INFO - config.py:98 - Loaded configuration from S:\Automatisation\tiktok_automatisation\config.json
2025-05-23 18:44:44,679 - INFO - db_manager.py:30 - Connected to database at S:\Automatisation\tiktok_automatisation\data\tiktok_automation.db
2025-05-23 18:44:44,686 - INFO - db_manager.py:91 - Database initialized successfully
2025-05-23 18:44:44,687 - INFO - db_manager.py:41 - Database connection closed
2025-05-23 18:44:44,778 - INFO - db_manager.py:30 - Connected to database at S:\Automatisation\tiktok_automatisation\data\tiktok_automation.db
2025-05-23 18:44:44,778 - INFO - db_manager.py:41 - Database connection closed
2025-05-23 18:44:44,778 - CRITICAL - main.py:54 - Unhandled exception: 'AppWindow' object has no attribute 'status_var'
Traceback (most recent call last):
  File "S:\Automatisation\tiktok_automatisation\src\gui\account_frame.py", line 185, in refresh_accounts
    self.set_status(f"Loaded {len(accounts)} accounts")
  File "S:\Automatisation\tiktok_automatisation\src\gui\app_window.py", line 119, in set_status
    self.status_var.set(f"[{timestamp}] {message}")
    ^^^^^^^^^^^^^^^
AttributeError: 'AppWindow' object has no attribute 'status_var'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "S:\Automatisation\tiktok_automatisation\main.py", line 52, in <module>
    main()
  File "S:\Automatisation\tiktok_automatisation\main.py", line 45, in main
    app = AppWindow(root, db_manager, config)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Automatisation\tiktok_automatisation\src\gui\app_window.py", line 56, in __init__
    self.create_tabs()
  File "S:\Automatisation\tiktok_automatisation\src\gui\app_window.py", line 77, in create_tabs
    self.accounts_frame = AccountFrame(self.notebook, self.db_manager, self.config, self.set_status)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Automatisation\tiktok_automatisation\src\gui\account_frame.py", line 34, in __init__
    self.refresh_accounts()
  File "S:\Automatisation\tiktok_automatisation\src\gui\account_frame.py", line 187, in refresh_accounts
    self.set_status(f"Error loading accounts: {str(e)}")
  File "S:\Automatisation\tiktok_automatisation\src\gui\app_window.py", line 119, in set_status
    self.status_var.set(f"[{timestamp}] {message}")
    ^^^^^^^^^^^^^^^
AttributeError: 'AppWindow' object has no attribute 'status_var'
