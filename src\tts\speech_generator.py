"""
Text-to-Speech Generator for TikTok Automation
Converts text to speech using various TTS engines
"""

import os
import logging
import time
from typing import Dict, Any, Optional, List
import tempfile
import shutil

# For TTS library
try:
    import TTS
    from TTS.utils.manage import ModelManager
    from TTS.utils.synthesizer import Synthesizer
except ImportError:
    logging.warning("TTS package not installed. TTS speech generation will not be available.")

# For Bark TTS (if available)
try:
    from bark import SAMPLE_RATE, generate_audio, preload_models
except ImportError:
    logging.warning("Bark TTS package not installed. Bark speech generation will not be available.")

class SpeechGenerator:
    """Generates speech from text for TikTok videos"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize speech generator
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.provider = config['tts']['provider']
        self.voice_options = config['tts']['voice_options']
        
        # Create output directory
        self.output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
                                      "data", "audio")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Initialize provider-specific resources
        if self.provider == 'tts':
            self._init_tts()
        elif self.provider == 'bark':
            self._init_bark()
        else:
            raise ValueError(f"Unsupported TTS provider: {self.provider}")
    
    def _init_tts(self):
        """Initialize TTS library"""
        try:
            # Initialize TTS
            self.model_manager = ModelManager()
            
            # Get available models
            self.available_models = self.model_manager.list_models()
            
            # Select a default model
            model_path, config_path, model_item = self.model_manager.download_model("tts_models/en/ljspeech/tacotron2-DDC")
            vocoder_path, vocoder_config_path, _ = self.model_manager.download_model("vocoder_models/en/ljspeech/multiband-melgan")
            
            # Initialize synthesizer
            self.synthesizer = Synthesizer(
                model_path,
                config_path,
                vocoder_path,
                vocoder_config_path,
                use_cuda=False
            )
            
            logging.info("Initialized TTS speech generator")
        except Exception as e:
            logging.error(f"Error initializing TTS: {str(e)}")
            raise
    
    def _init_bark(self):
        """Initialize Bark TTS"""
        try:
            # Preload Bark models
            preload_models()
            logging.info("Initialized Bark speech generator")
        except Exception as e:
            logging.error(f"Error initializing Bark: {str(e)}")
            raise
    
    def generate_speech(self, text: str, voice_type: str, output_filename: Optional[str] = None) -> str:
        """Generate speech from text
        
        Args:
            text: Text to convert to speech
            voice_type: Type of voice to use (e.g., "male", "female", "ai")
            output_filename: Optional filename for the output audio file
            
        Returns:
            str: Path to the generated audio file
        """
        logging.info(f"Generating speech for text ({len(text)} chars) with voice type: {voice_type}")
        
        # Select voice based on voice_type
        voice = self._select_voice(voice_type)
        
        # Generate filename if not provided
        if not output_filename:
            timestamp = int(time.time())
            output_filename = f"speech_{timestamp}.wav"
        
        output_path = os.path.join(self.output_dir, output_filename)
        
        # Generate speech using selected provider
        if self.provider == 'tts':
            self._generate_with_tts(text, voice, output_path)
        elif self.provider == 'bark':
            self._generate_with_bark(text, voice, output_path)
        else:
            raise ValueError(f"Unsupported TTS provider: {self.provider}")
        
        logging.info(f"Generated speech saved to: {output_path}")
        return output_path
    
    def _select_voice(self, voice_type: str) -> str:
        """Select a voice based on voice type
        
        Args:
            voice_type: Type of voice (e.g., "male", "female", "ai")
            
        Returns:
            str: Selected voice identifier
        """
        # Get available voices for the requested type
        available_voices = self.voice_options.get(voice_type.lower(), [])
        
        if not available_voices:
            logging.warning(f"No voices available for type '{voice_type}', using default")
            # Try to get any voice
            for voices in self.voice_options.values():
                if voices:
                    return voices[0]
            raise ValueError(f"No voices available")
        
        # Return the first voice of the requested type
        return available_voices[0]
    
    def _generate_with_tts(self, text: str, voice: str, output_path: str) -> None:
        """Generate speech using TTS library
        
        Args:
            text: Text to convert to speech
            voice: Voice identifier
            output_path: Path to save the generated audio
        """
        try:
            # Process text to handle TTS limitations
            processed_text = self._preprocess_text(text)
            
            # Generate speech
            wav = self.synthesizer.tts(processed_text)
            
            # Save to file
            self.synthesizer.save_wav(wav, output_path)
        except Exception as e:
            logging.error(f"Error generating speech with TTS: {str(e)}")
            raise
    
    def _generate_with_bark(self, text: str, voice: str, output_path: str) -> None:
        """Generate speech using Bark TTS
        
        Args:
            text: Text to convert to speech
            voice: Voice identifier
            output_path: Path to save the generated audio
        """
        try:
            import scipy.io.wavfile as wav
            import numpy as np
            
            # Process text to handle Bark limitations
            processed_text = self._preprocess_text(text)
            
            # Generate speech
            audio_array = generate_audio(processed_text, history_prompt=voice)
            
            # Save to file
            wav.write(output_path, SAMPLE_RATE, audio_array)
        except Exception as e:
            logging.error(f"Error generating speech with Bark: {str(e)}")
            raise
    
    def _preprocess_text(self, text: str) -> str:
        """Preprocess text for TTS
        
        Args:
            text: Original text
            
        Returns:
            str: Processed text suitable for TTS
        """
        # Remove special characters that might cause issues
        processed_text = text.replace('"', '').replace('"', '')
        
        # Split into sentences if text is too long
        if len(processed_text) > 1000:
            logging.info("Text is long, splitting into chunks for processing")
            return self._split_into_sentences(processed_text)
        
        return processed_text
    
    def _split_into_sentences(self, text: str) -> str:
        """Split long text into sentences
        
        Args:
            text: Long text
            
        Returns:
            str: Text with proper sentence breaks
        """
        # Simple sentence splitting
        sentences = []
        for sentence in text.replace('. ', '.\n').replace('! ', '!\n').replace('? ', '?\n').split('\n'):
            if sentence.strip():
                sentences.append(sentence.strip())
        
        return '. '.join(sentences)
    
    def list_available_voices(self) -> Dict[str, List[str]]:
        """List all available voices
        
        Returns:
            Dict: Dictionary of voice types and available voices
        """
        return self.voice_options
