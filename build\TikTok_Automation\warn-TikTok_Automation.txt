
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), http.server (delayed, optional), netrc (delayed, conditional), getpass (delayed), setuptools._distutils.util (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named posix - imported by os (conditional, optional), shutil (conditional), importlib._bootstrap_external (conditional), posixpath (optional)
missing module named resource - imported by posix (top-level), fsspec.asyn (conditional, optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional), tqdm.utils (delayed, optional), filelock._unix (conditional, optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by tty (top-level), getpass (optional), click._termui_impl (conditional), tqdm.utils (delayed, optional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.cpu_count - imported by multiprocessing (top-level), transformers.data.processors.squad (top-level)
missing module named multiprocessing.Pool - imported by multiprocessing (top-level), transformers.data.processors.squad (top-level), transformers.models.nougat.tokenization_nougat_fast (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), site (delayed, optional), rlcompleter (optional)
missing module named _typeshed - imported by numpy.random.bit_generator (top-level), pydantic_core._pydantic_core (top-level), pydantic._internal._dataclasses (conditional), anyio.abc._eventloop (conditional), anyio._core._sockets (conditional), anyio._core._fileio (conditional), anyio._core._tempfile (conditional), httpx._transports.wsgi (conditional), setuptools._distutils.dist (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), pkg_resources (conditional), anyio._backends._asyncio (conditional), anyio._core._asyncio_selector_thread (conditional), anyio._backends._trio (conditional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named jnius - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named android - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional), tqdm.cli (delayed, conditional, optional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named pyimod02_importers - imported by S:\Automatisation\tiktok_automatisation\venv\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), S:\Automatisation\tiktok_automatisation\venv\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named win32evtlog - imported by logging.handlers (delayed, optional)
missing module named win32evtlogutil - imported by logging.handlers (delayed, optional)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecmat - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.matvec - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named threadpoolctl - imported by numpy.lib._utils_impl (delayed, optional)
missing module named psutil - imported by numpy.testing._private.utils (delayed, optional), transformers.trainer_utils (delayed), transformers.modeling_utils (delayed, optional)
missing module named win32pdh - imported by numpy.testing._private.utils (delayed, conditional)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.lib._function_base_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.lib._utils_impl (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.lib._array_utils_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level), numpy.fft._helper (top-level)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named numpy.random.RandomState - imported by numpy.random (top-level), numpy.random._generator (top-level)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named tifffile - imported by imageio.plugins.tifffile (optional), imageio.plugins.tifffile_v3 (top-level)
missing module named SimpleITK - imported by imageio.plugins.simpleitk (delayed, optional)
missing module named itk - imported by imageio.plugins.simpleitk (delayed, optional)
missing module named rawpy - imported by imageio.plugins.rawpy (top-level)
missing module named 'av.codec' - imported by imageio.plugins.pyav (top-level)
missing module named 'av.filter' - imported by imageio.plugins.pyav (top-level)
missing module named av - imported by transformers.video_utils (delayed), transformers.pipelines.video_classification (conditional), imageio.plugins.pyav (top-level)
missing module named pillow_heif - imported by imageio.plugins.pillow (delayed, optional)
missing module named osgeo - imported by imageio.plugins.gdal (delayed, optional)
missing module named astropy - imported by imageio.plugins.fits (delayed, optional)
missing module named matplotlib - imported by tqdm.gui (delayed), imageio.plugins._tifffile (delayed, conditional, optional)
missing module named tkFileDialog - imported by imageio.plugins._tifffile (delayed, optional)
missing module named Tkinter - imported by imageio.plugins._tifffile (delayed, optional)
missing module named lxml - imported by imageio.plugins._tifffile (delayed, optional)
missing module named tifffile_geodb - imported by imageio.plugins._tifffile (delayed, optional)
missing module named imageio.plugins.tifffile_geodb - imported by imageio.plugins._tifffile (delayed, optional)
missing module named imagecodecs - imported by imageio.plugins._tifffile (delayed, conditional, optional)
missing module named zstd - imported by imageio.plugins._tifffile (delayed, conditional, optional)
missing module named 'backports.lzma' - imported by imageio.plugins._tifffile (delayed, conditional, optional)
missing module named bsdf_cli - imported by imageio.plugins._bsdf (conditional)
missing module named imp - imported by imageio.core.util (delayed, conditional)
missing module named 'IPython.core' - imported by dotenv.ipython (top-level)
missing module named IPython - imported by dotenv.ipython (top-level)
missing module named 'IPython.display' - imported by tqdm.notebook (conditional, optional), huggingface_hub._login (delayed, optional), transformers.utils.notebook (top-level), moviepy.video.io.display_in_notebook (optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional)
missing module named setuptools_scm - imported by tqdm.version (optional)
missing module named pandas - imported by openai._extras.pandas_proxy (delayed, conditional, optional), tqdm.std (delayed, optional), transformers.integrations.integration_utils (delayed, conditional), transformers.models.deprecated.tapex.tokenization_tapex (conditional), transformers.models.tapas.tokenization_tapas (conditional), transformers.pipelines.table_question_answering (delayed), fsspec.implementations.reference (delayed)
missing module named 'pandas.core' - imported by tqdm.std (delayed, optional)
missing module named 'matplotlib.pyplot' - imported by tqdm.gui (delayed)
missing module named 'moviepy.editor' - imported by src.video_generation.video_generator (optional)
missing module named simplejson - imported by requests.compat (conditional, optional), huggingface_hub.utils._fixes (optional)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named 'h2.events' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level), urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level), urllib3.http2.connection (top-level)
missing module named 'h2.config' - imported by httpcore._async.http2 (top-level), urllib3.http2.connection (top-level)
missing module named zstandard - imported by httpx._decoders (optional), urllib3.util.request (optional), urllib3.response (optional), fsspec.compression (optional)
missing module named brotli - imported by httpx._decoders (optional), urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by httpx._decoders (optional), urllib3.util.request (optional), urllib3.response (optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named cryptography - imported by urllib3.contrib.pyopenssl (top-level), requests (conditional, optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named 'cryptography.x509' - imported by urllib3.contrib.pyopenssl (delayed, optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named chardet - imported by requests (optional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level), fsspec.implementations.http_sync (delayed, optional)
missing module named pydub - imported by src.tts.speech_generator (delayed, conditional, optional)
missing module named 'scipy.io' - imported by src.tts.speech_generator (optional)
missing module named tensorflow - imported by huggingface_hub.keras_mixin (conditional, optional), huggingface_hub.serialization._tensorflow (delayed, conditional), transformers.utils.generic (delayed, conditional), transformers.feature_extraction_utils (delayed, conditional), transformers.image_transforms (conditional), transformers.tokenization_utils_base (delayed, conditional), transformers.models.encoder_decoder.modeling_tf_encoder_decoder (top-level), transformers.modeling_tf_outputs (top-level), transformers.modeling_tf_utils (top-level), transformers.activations_tf (top-level), transformers.tf_utils (top-level), safetensors.tensorflow (top-level), transformers.trainer_utils (delayed, conditional), transformers.modelcard (delayed, conditional), transformers.modeling_tf_pytorch_utils (delayed, optional), transformers.onnx.convert (delayed), transformers.models.roformer.modeling_roformer (delayed, optional), transformers.models.roformer.modeling_tf_roformer (top-level), transformers.generation.tf_logits_process (top-level), transformers.generation.tf_utils (top-level), transformers.models.bert.modeling_bert (delayed, optional), transformers.models.bert.modeling_tf_bert (top-level), transformers.models.bert.tokenization_bert_tf (top-level), transformers.data.data_collator (delayed, conditional), transformers.data.processors.utils (delayed, conditional), transformers.data.processors.glue (conditional), transformers.data.processors.squad (conditional), transformers.models.albert.modeling_albert (delayed, optional), transformers.models.albert.modeling_tf_albert (top-level), transformers.models.bart.modeling_tf_bart (top-level), transformers.models.big_bird.modeling_big_bird (delayed, optional), transformers.models.blenderbot_small.modeling_tf_blenderbot_small (top-level), transformers.models.blenderbot.modeling_tf_blenderbot (top-level), transformers.models.blip.modeling_tf_blip (top-level), transformers.models.blip.modeling_tf_blip_text (top-level), transformers.models.camembert.modeling_tf_camembert (top-level), transformers.models.canine.modeling_canine (delayed, optional), transformers.models.clip.modeling_tf_clip (top-level), transformers.models.codegen.tokenization_codegen (conditional), transformers.models.codegen.tokenization_codegen_fast (conditional), transformers.models.conditional_detr.image_processing_conditional_detr (delayed, conditional), transformers.models.convbert.modeling_convbert (delayed, optional), transformers.models.convbert.modeling_tf_convbert (top-level), transformers.models.convnext.modeling_tf_convnext (top-level), transformers.models.convnextv2.modeling_tf_convnextv2 (top-level), transformers.models.ctrl.modeling_tf_ctrl (top-level), transformers.models.cvt.modeling_tf_cvt (top-level), transformers.models.data2vec.modeling_tf_data2vec_vision (top-level), transformers.models.deberta.modeling_tf_deberta (top-level), transformers.models.deberta_v2.modeling_tf_deberta_v2 (top-level), transformers.models.decision_transformer.modeling_decision_transformer (delayed, optional), transformers.models.deformable_detr.image_processing_deformable_detr (delayed, conditional), transformers.models.deit.modeling_tf_deit (top-level), transformers.models.deprecated.deta.image_processing_deta (delayed, conditional), transformers.models.deprecated.efficientformer.modeling_tf_efficientformer (top-level), transformers.models.deprecated.jukebox.tokenization_jukebox (delayed, conditional), transformers.models.deprecated.nezha.modeling_nezha (delayed, optional), transformers.models.deprecated.qdqbert.modeling_qdqbert (delayed, optional), transformers.models.deprecated.realm.modeling_realm (delayed, optional), transformers.models.deprecated.trajectory_transformer.modeling_trajectory_transformer (delayed, optional), transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl (top-level), transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (delayed, optional), transformers.models.detr.image_processing_detr (delayed, conditional), transformers.models.distilbert.modeling_tf_distilbert (top-level), transformers.models.dpr.modeling_tf_dpr (top-level), transformers.models.electra.modeling_electra (delayed, optional), transformers.models.electra.modeling_tf_electra (top-level), transformers.models.esm.modeling_tf_esm (top-level), transformers.models.flaubert.modeling_tf_flaubert (top-level), transformers.models.funnel.modeling_funnel (delayed, optional), transformers.models.funnel.modeling_tf_funnel (top-level), transformers.models.gpt2.modeling_gpt2 (delayed, optional), transformers.models.gpt2.modeling_tf_gpt2 (top-level), transformers.models.gpt2.tokenization_gpt2_tf (top-level), transformers.models.gpt_neo.modeling_gpt_neo (delayed, optional), transformers.models.gptj.modeling_tf_gptj (top-level), transformers.models.grounding_dino.image_processing_grounding_dino (delayed, conditional), transformers.models.groupvit.modeling_tf_groupvit (top-level), transformers.models.hubert.modeling_tf_hubert (top-level), transformers.models.idefics.modeling_tf_idefics (top-level), transformers.models.idefics.perceiver_tf (top-level), transformers.models.idefics.vision_tf (top-level), transformers.models.idefics.processing_idefics (conditional), transformers.models.imagegpt.modeling_imagegpt (delayed, optional), transformers.models.layoutlm.modeling_tf_layoutlm (top-level), transformers.models.layoutlmv3.modeling_tf_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_tf_xlm_roberta (top-level), transformers.models.led.modeling_tf_led (top-level), transformers.models.longformer.modeling_tf_longformer (top-level), transformers.models.lxmert.modeling_lxmert (delayed, optional), transformers.models.lxmert.modeling_tf_lxmert (top-level), transformers.models.marian.modeling_tf_marian (top-level), transformers.models.swin.modeling_tf_swin (top-level), transformers.models.mbart.modeling_tf_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (delayed, optional), transformers.models.mistral.modeling_tf_mistral (top-level), transformers.models.mobilebert.modeling_mobilebert (delayed, optional), transformers.models.mobilebert.modeling_tf_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (delayed, optional), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (delayed, optional), transformers.models.mobilevit.modeling_tf_mobilevit (top-level), transformers.models.mpnet.modeling_tf_mpnet (top-level), transformers.models.t5.modeling_t5 (delayed, optional), transformers.models.t5.modeling_tf_t5 (top-level), transformers.models.mt5.modeling_mt5 (delayed, optional), transformers.models.openai.modeling_tf_openai (top-level), transformers.models.opt.modeling_tf_opt (top-level), transformers.models.owlv2.processing_owlv2 (delayed, conditional), transformers.models.owlvit.processing_owlvit (delayed, conditional), transformers.models.pegasus.modeling_tf_pegasus (top-level), transformers.models.rag.modeling_tf_rag (top-level), transformers.models.regnet.modeling_tf_regnet (top-level), transformers.models.rembert.modeling_rembert (delayed, optional), transformers.models.rembert.modeling_tf_rembert (top-level), transformers.models.resnet.modeling_tf_resnet (top-level), transformers.models.roberta.modeling_tf_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_tf_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (delayed, optional), transformers.models.rt_detr.image_processing_rt_detr (delayed, conditional), transformers.models.sam.image_processing_sam (conditional), transformers.models.sam.modeling_tf_sam (top-level), transformers.models.sam.processing_sam (conditional), transformers.models.segformer.modeling_tf_segformer (top-level), transformers.models.speech_to_text.modeling_tf_speech_to_text (top-level), transformers.models.swiftformer.modeling_tf_swiftformer (top-level), transformers.models.tapas.modeling_tapas (delayed, optional), transformers.models.tapas.modeling_tf_tapas (top-level), transformers.models.vision_encoder_decoder.modeling_tf_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_tf_vit (top-level), transformers.models.vit_mae.modeling_tf_vit_mae (top-level), transformers.models.wav2vec2.modeling_tf_wav2vec2 (top-level), transformers.models.wav2vec2.tokenization_wav2vec2 (conditional), transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (conditional), transformers.models.whisper.modeling_tf_whisper (top-level), transformers.models.xglm.modeling_tf_xglm (top-level), transformers.models.xlm.modeling_tf_xlm (top-level), transformers.models.xlnet.modeling_tf_xlnet (top-level), transformers.models.xlnet.modeling_xlnet (delayed, optional), transformers.models.yolos.image_processing_yolos (delayed, conditional), transformers.pipelines.base (conditional), transformers.pipelines.question_answering (conditional), transformers.pipelines.fill_mask (conditional), transformers.pipelines.table_question_answering (conditional), transformers.pipelines.text2text_generation (conditional), transformers.pipelines.text_generation (conditional), transformers.pipelines.token_classification (conditional), transformers.pipelines (conditional), transformers.training_args_tf (conditional), transformers.keras_callbacks (top-level), transformers.optimization_tf (top-level)
missing module named flax - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_outputs (top-level), transformers.generation.flax_utils (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level)
missing module named 'torch.nn' - imported by transformers.integrations.aqlm (conditional), transformers.modeling_utils (top-level), transformers.models.encoder_decoder.modeling_encoder_decoder (top-level), transformers.models.roformer.modeling_roformer (top-level), transformers.generation.candidate_generator (top-level), transformers.generation.stopping_criteria (top-level), transformers.generation.utils (top-level), transformers.generation.watermarking (top-level), transformers.integrations.accelerate (conditional), transformers.modeling_flash_attention_utils (top-level), transformers.integrations.npu_flash_attention (top-level), transformers.integrations.flex_attention (conditional), transformers.loss.loss_utils (top-level), transformers.loss.loss_d_fine (top-level), transformers.loss.loss_for_object_detection (top-level), transformers.loss.loss_rt_detr (top-level), transformers.loss.loss_deformable_detr (top-level), transformers.loss.loss_grounding_dino (top-level), transformers.quantizers.base (conditional), transformers.models.llama4.modeling_llama4 (top-level), transformers.integrations.finegrained_fp8 (conditional), transformers.quantizers.quantizer_torchao (conditional), transformers.integrations.awq (conditional), transformers.integrations.bitnet (conditional), transformers.modeling_layers (top-level), transformers.models.llama.modeling_llama (conditional), transformers.integrations.bitsandbytes (conditional), transformers.integrations.eetq (conditional), transformers.models.bert.modeling_bert (top-level), transformers.data.data_collator (delayed), transformers.trainer (delayed, conditional), transformers.integrations.spqr (conditional), transformers.integrations.vptq (top-level), transformers.models.albert.modeling_albert (top-level), transformers.models.altclip.modeling_altclip (top-level), transformers.models.aria.modeling_aria (conditional), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.jamba.modeling_jamba (top-level), transformers.models.bark.modeling_bark (top-level), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.bigbird_pegasus.modeling_bigbird_pegasus (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.bitnet.modeling_bitnet (conditional), transformers.models.blenderbot.modeling_blenderbot (top-level), transformers.models.blenderbot_small.modeling_blenderbot_small (top-level), transformers.models.blip.modeling_blip (top-level), transformers.models.blip.modeling_blip_text (top-level), transformers.models.blip_2.modeling_blip_2 (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.bridgetower.modeling_bridgetower (top-level), transformers.models.bros.modeling_bros (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.chameleon.modeling_chameleon (top-level), transformers.models.clap.modeling_clap (top-level), transformers.models.clip.modeling_clip (top-level), transformers.models.clvp.modeling_clvp (top-level), transformers.models.codegen.modeling_codegen (conditional), transformers.models.cohere.modeling_cohere (conditional), transformers.models.cohere2.modeling_cohere2 (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.cpmant.modeling_cpmant (top-level), transformers.models.csm.modeling_csm (top-level), transformers.models.csm.generation_csm (top-level), transformers.models.ctrl.modeling_ctrl (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.d_fine.modeling_d_fine (top-level), transformers.models.dac.modeling_dac (top-level), transformers.models.data2vec.modeling_data2vec_audio (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.dbrx.modeling_dbrx (conditional), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deformable_detr.modeling_deformable_detr (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.deprecated.deta.modeling_deta (top-level), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.gptsan_japanese.modeling_gptsan_japanese (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.jukebox.modeling_jukebox (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.mmbt.modeling_mmbt (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.realm.modeling_realm (top-level), transformers.models.deprecated.speech_to_text_2.modeling_speech_to_text_2 (top-level), transformers.models.deprecated.trajectory_transformer.modeling_trajectory_transformer (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level), transformers.models.deprecated.xlm_prophetnet.modeling_xlm_prophetnet (top-level), transformers.models.depth_pro.modeling_depth_pro (top-level), transformers.models.diffllama.modeling_diffllama (conditional), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.modeling_distilbert (top-level), transformers.models.dpt.modeling_dpt (top-level), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.emu3.modeling_emu3 (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.esm.modeling_esmfold (top-level), transformers.models.esm.openfold_utils.tensor_utils (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.falcon_mamba.modeling_falcon_mamba (top-level), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level), transformers.models.flaubert.modeling_flaubert (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.fsmt.modeling_fsmt (top-level), transformers.models.funnel.modeling_funnel (top-level), transformers.models.gemma.modeling_gemma (conditional), transformers.models.gemma2.modeling_gemma2 (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.gemma3.modeling_gemma3 (top-level), transformers.models.glm.modeling_glm (top-level), transformers.models.glm4.modeling_glm4 (top-level), transformers.models.got_ocr2.modeling_got_ocr2 (top-level), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gpt_neox.modeling_gpt_neox (conditional), transformers.models.gpt_neox_japanese.modeling_gpt_neox_japanese (conditional), transformers.models.gptj.modeling_gptj (top-level), transformers.models.granite.modeling_granite (conditional), transformers.models.granite_speech.modeling_granite_speech (top-level), transformers.models.granitemoe.modeling_granitemoe (top-level), transformers.models.granitemoehybrid.modeling_granitemoehybrid (top-level), transformers.models.granitemoeshared.modeling_granitemoeshared (top-level), transformers.models.grounding_dino.modeling_grounding_dino (top-level), transformers.models.helium.modeling_helium (top-level), transformers.models.hgnet_v2.modeling_hgnet_v2 (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.hubert.modeling_hubert (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.idefics.modeling_idefics (top-level), transformers.models.idefics.perceiver (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.internvl.modeling_internvl (top-level), transformers.models.janus.modeling_janus (conditional), transformers.models.jetmoe.modeling_jetmoe (top-level), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.longformer.modeling_longformer (top-level), transformers.models.longt5.modeling_longt5 (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.lxmert.modeling_lxmert (top-level), transformers.models.m2m_100.modeling_m2m_100 (top-level), transformers.models.mamba.modeling_mamba (top-level), transformers.models.marian.modeling_marian (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mgp_str.modeling_mgp_str (top-level), transformers.models.mimi.modeling_mimi (conditional), transformers.models.mistral.modeling_mistral (conditional), transformers.models.mixtral.modeling_mixtral (top-level), transformers.models.mlcd.modeling_mlcd (top-level), transformers.models.mllama.modeling_mllama (top-level), transformers.models.mobilebert.modeling_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (top-level), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.modernbert.modeling_modernbert (top-level), transformers.models.moonshine.modeling_moonshine (top-level), transformers.models.moshi.modeling_moshi (top-level), transformers.models.mpnet.modeling_mpnet (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.t5.modeling_t5 (top-level), transformers.models.mt5.modeling_mt5 (top-level), transformers.models.musicgen.modeling_musicgen (top-level), transformers.models.musicgen_melody.modeling_musicgen_melody (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nemotron.modeling_nemotron (top-level), transformers.models.nllb_moe.modeling_nllb_moe (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.olmo.modeling_olmo (top-level), transformers.models.olmo2.modeling_olmo2 (top-level), transformers.models.olmoe.modeling_olmoe (top-level), transformers.models.omdet_turbo.modeling_omdet_turbo (top-level), transformers.models.openai.modeling_openai (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.patchtsmixer.modeling_patchtsmixer (top-level), transformers.models.pegasus.modeling_pegasus (top-level), transformers.models.pegasus_x.modeling_pegasus_x (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.persimmon.modeling_persimmon (conditional), transformers.models.phi.modeling_phi (top-level), transformers.models.phi3.modeling_phi3 (conditional), transformers.models.phi4_multimodal.modeling_phi4_multimodal (top-level), transformers.models.phimoe.modeling_phimoe (conditional), transformers.models.pix2struct.modeling_pix2struct (conditional), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.pop2piano.modeling_pop2piano (top-level), transformers.models.prophetnet.modeling_prophetnet (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.qwen2.modeling_qwen2 (conditional), transformers.models.qwen2_5_vl.modeling_qwen2_5_vl (top-level), transformers.models.qwen2_moe.modeling_qwen2_moe (top-level), transformers.models.qwen2_vl.modeling_qwen2_vl (top-level), transformers.models.reformer.modeling_reformer (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.rt_detr.modeling_rt_detr (top-level), transformers.models.rt_detr_v2.modeling_rt_detr_v2 (top-level), transformers.models.sam.image_processing_sam (conditional), transformers.models.sam.modeling_sam (top-level), transformers.models.sam_hq.modeling_sam_hq (top-level), transformers.models.seamless_m4t.modeling_seamless_m4t (top-level), transformers.models.seamless_m4t_v2.modeling_seamless_m4t_v2 (top-level), transformers.models.segformer.modeling_segformer (top-level), transformers.models.seggpt.modeling_seggpt (top-level), transformers.models.sew.modeling_sew (top-level), transformers.models.sew_d.modeling_sew_d (top-level), transformers.models.siglip2.modeling_siglip2 (top-level), transformers.models.speech_encoder_decoder.modeling_speech_encoder_decoder (top-level), transformers.models.speech_to_text.modeling_speech_to_text (top-level), transformers.models.speecht5.modeling_speecht5 (top-level), transformers.models.splinter.modeling_splinter (top-level), transformers.models.squeezebert.modeling_squeezebert (top-level), transformers.models.stablelm.modeling_stablelm (conditional), transformers.models.starcoder2.modeling_starcoder2 (conditional), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.switch_transformers.modeling_switch_transformers (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.textnet.modeling_textnet (top-level), transformers.models.timesfm.modeling_timesfm (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.timm_wrapper.modeling_timm_wrapper (top-level), transformers.models.trocr.modeling_trocr (top-level), transformers.models.udop.modeling_udop (top-level), transformers.models.umt5.modeling_umt5 (top-level), transformers.models.unispeech.modeling_unispeech (top-level), transformers.models.unispeech_sat.modeling_unispeech_sat (top-level), transformers.models.upernet.modeling_upernet (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vilt.modeling_vilt (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.vivit.modeling_vivit (top-level), transformers.models.wav2vec2.modeling_wav2vec2 (top-level), transformers.models.wav2vec2_bert.modeling_wav2vec2_bert (top-level), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (top-level), transformers.models.wavlm.modeling_wavlm (top-level), transformers.models.whisper.modeling_whisper (top-level), transformers.models.whisper.generation_whisper (top-level), transformers.models.xlm.modeling_xlm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xlnet.modeling_xlnet (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), transformers.models.zamba2.modeling_zamba2 (top-level)
missing module named torch - imported by huggingface_hub._tensorboard_logger (optional), huggingface_hub.hub_mixin (conditional), safetensors.torch (top-level), huggingface_hub.serialization._torch (delayed, conditional, optional), transformers.utils.import_utils (delayed, conditional, optional), transformers.utils.generic (delayed, conditional), transformers.activations (top-level), transformers.modeling_utils (top-level), transformers.utils.deprecation (conditional), transformers.cache_utils (top-level), transformers.pytorch_utils (top-level), transformers.generation.logits_process (top-level), transformers.models.timm_backbone.modeling_timm_backbone (top-level), transformers.modeling_outputs (top-level), transformers.feature_extraction_utils (delayed, conditional), transformers.image_utils (delayed, conditional), transformers.image_transforms (conditional), transformers.utils.chat_template_utils (conditional), transformers.video_utils (conditional), transformers.tokenization_utils_base (delayed, conditional), transformers.models.encoder_decoder.modeling_encoder_decoder (top-level), transformers.modeling_flax_pytorch_utils (delayed, conditional, optional), transformers.debug_utils (conditional), transformers.trainer_utils (delayed, conditional), transformers.training_args (conditional), transformers.trainer_pt_utils (top-level), transformers.integrations.deepspeed (conditional), transformers.modelcard (delayed, conditional), transformers.modeling_tf_pytorch_utils (delayed, optional), transformers.onnx.config (delayed, conditional), transformers.onnx.convert (delayed, conditional), transformers.models.roformer.modeling_roformer (top-level), transformers.image_processing_utils_fast (conditional), transformers.video_processing_utils (conditional), transformers.generation.beam_search (top-level), transformers.generation.candidate_generator (top-level), transformers.generation.stopping_criteria (top-level), transformers.generation.utils (top-level), transformers.integrations.fsdp (delayed, conditional), transformers.generation.watermarking (top-level), transformers.integrations.accelerate (conditional), transformers.integrations.flash_attention (top-level), transformers.modeling_flash_attention_utils (top-level), transformers.integrations.npu_flash_attention (top-level), transformers.integrations.flex_attention (top-level), transformers.integrations.sdpa_attention (top-level), transformers.integrations.tensor_parallel (top-level), transformers.loss.loss_utils (top-level), transformers.loss.loss_d_fine (top-level), transformers.loss.loss_for_object_detection (top-level), transformers.loss.loss_rt_detr (top-level), transformers.loss.loss_deformable_detr (top-level), transformers.loss.loss_grounding_dino (top-level), transformers.utils.quantization_config (conditional), transformers.quantizers.base (conditional), transformers.models.llama4.image_processing_llama4_fast (conditional), transformers.models.llama4.modeling_llama4 (top-level), transformers.modeling_attn_mask_utils (top-level), transformers.modeling_rope_utils (conditional), transformers.quantizers.quantizer_aqlm (conditional), transformers.quantizers.quantizer_auto_round (conditional), transformers.quantizers.quantizer_awq (conditional), transformers.quantizers.quantizer_bitnet (conditional), transformers.quantizers.quantizer_bnb_4bit (conditional), transformers.quantizers.quantizer_bnb_8bit (conditional), transformers.quantizers.quantizer_compressed_tensors (conditional), transformers.quantizers.quantizer_eetq (conditional), transformers.quantizers.quantizer_fbgemm_fp8 (conditional), transformers.quantizers.quantizer_finegrained_fp8 (conditional), transformers.integrations.finegrained_fp8 (conditional), transformers.quantizers.quantizer_gptq (conditional), transformers.quantizers.quantizer_higgs (conditional), transformers.quantizers.quantizer_hqq (conditional), transformers.quantizers.quantizer_quanto (conditional), transformers.quantizers.quantizer_quark (conditional), transformers.quantizers.quantizer_spqr (conditional), transformers.quantizers.quantizer_torchao (conditional), transformers.quantizers.quantizer_vptq (conditional), transformers.integrations.awq (conditional), transformers.integrations.bitnet (conditional), transformers.models.llama.modeling_llama (top-level), transformers.integrations.bitsandbytes (delayed, conditional), transformers.integrations.fbgemm_fp8 (conditional), transformers.integrations.higgs (conditional), transformers.integrations.hqq (conditional), transformers.integrations.integration_utils (conditional), transformers.trainer (top-level), transformers.models.bert.modeling_bert (top-level), transformers.data.data_collator (delayed, conditional), transformers.data.processors.utils (delayed, conditional), transformers.data.processors.squad (conditional), transformers.optimization (top-level), transformers.integrations.peft (conditional), transformers.integrations.quanto (conditional), transformers.integrations.executorch (top-level), transformers.modeling_gguf_pytorch_utils (conditional), transformers.configuration_utils (delayed, conditional), transformers.models.albert.modeling_albert (top-level), transformers.models.align.modeling_align (top-level), transformers.models.altclip.modeling_altclip (top-level), transformers.models.aria.modeling_aria (conditional), transformers.models.audio_spectrogram_transformer.feature_extraction_audio_spectrogram_transformer (conditional), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.autoformer.modeling_autoformer (top-level), transformers.time_series_utils (top-level), transformers.models.aya_vision.modeling_aya_vision (top-level), transformers.models.bamba.modeling_bamba (top-level), transformers.models.jamba.modeling_jamba (top-level), transformers.models.bark.modeling_bark (top-level), transformers.models.bart.configuration_bart (delayed, conditional), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.image_processing_beit (conditional), transformers.models.beit.image_processing_beit_fast (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.bert_generation.modeling_bert_generation (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.bigbird_pegasus.configuration_bigbird_pegasus (delayed, conditional), transformers.models.bigbird_pegasus.modeling_bigbird_pegasus (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.bitnet.modeling_bitnet (top-level), transformers.models.blenderbot.configuration_blenderbot (delayed, conditional), transformers.models.blenderbot.modeling_blenderbot (top-level), transformers.models.blenderbot_small.configuration_blenderbot_small (delayed, conditional), transformers.models.blenderbot_small.modeling_blenderbot_small (top-level), transformers.models.blip.modeling_blip (top-level), transformers.models.blip.modeling_blip_text (top-level), transformers.models.blip_2.modeling_blip_2 (top-level), transformers.models.bloom.configuration_bloom (delayed, conditional), transformers.models.bloom.modeling_bloom (top-level), transformers.models.bridgetower.image_processing_bridgetower_fast (conditional), transformers.models.bridgetower.modeling_bridgetower (top-level), transformers.models.bros.modeling_bros (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.chameleon.modeling_chameleon (top-level), transformers.models.chinese_clip.modeling_chinese_clip (top-level), transformers.models.clap.feature_extraction_clap (top-level), transformers.models.clap.modeling_clap (top-level), transformers.models.clip.modeling_clip (top-level), transformers.models.clipseg.modeling_clipseg (top-level), transformers.models.clvp.modeling_clvp (top-level), transformers.models.codegen.configuration_codegen (delayed, conditional), transformers.models.codegen.modeling_codegen (top-level), transformers.models.codegen.tokenization_codegen (conditional), transformers.models.codegen.tokenization_codegen_fast (conditional), transformers.models.cohere.modeling_cohere (top-level), transformers.models.cohere2.modeling_cohere2 (top-level), transformers.models.colpali.modeling_colpali (top-level), transformers.models.colpali.processing_colpali (conditional), transformers.models.conditional_detr.image_processing_conditional_detr (delayed, conditional), transformers.models.conditional_detr.image_processing_conditional_detr_fast (conditional), transformers.models.conditional_detr.modeling_conditional_detr (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.image_processing_convnext_fast (conditional), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.cpmant.modeling_cpmant (top-level), transformers.models.csm.modeling_csm (top-level), transformers.models.csm.generation_csm (top-level), transformers.models.csm.processing_csm (conditional), transformers.models.ctrl.modeling_ctrl (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.d_fine.modeling_d_fine (top-level), transformers.models.dab_detr.modeling_dab_detr (top-level), transformers.models.dac.modeling_dac (top-level), transformers.models.data2vec.modeling_data2vec_audio (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.dbrx.modeling_dbrx (top-level), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.decision_transformer.modeling_decision_transformer (top-level), transformers.models.deformable_detr.image_processing_deformable_detr (delayed, conditional), transformers.models.deformable_detr.image_processing_deformable_detr_fast (conditional), transformers.models.deformable_detr.modeling_deformable_detr (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.deprecated.deta.image_processing_deta (delayed, conditional), transformers.models.deprecated.deta.modeling_deta (top-level), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.gptsan_japanese.modeling_gptsan_japanese (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.jukebox.modeling_jukebox (top-level), transformers.models.deprecated.jukebox.tokenization_jukebox (delayed, conditional), transformers.models.deprecated.mctct.modeling_mctct (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.mmbt.modeling_mmbt (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.realm.modeling_realm (top-level), transformers.models.deprecated.retribert.modeling_retribert (top-level), transformers.models.deprecated.speech_to_text_2.modeling_speech_to_text_2 (top-level), transformers.models.deprecated.trajectory_transformer.modeling_trajectory_transformer (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl_utilities (top-level), transformers.models.deprecated.transfo_xl.tokenization_transfo_xl (conditional), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level), transformers.models.deprecated.xlm_prophetnet.modeling_xlm_prophetnet (top-level), transformers.models.depth_anything.modeling_depth_anything (top-level), transformers.models.depth_pro.modeling_depth_pro (top-level), transformers.models.depth_pro.image_processing_depth_pro (conditional), transformers.models.depth_pro.image_processing_depth_pro_fast (conditional), transformers.models.detr.image_processing_detr (delayed, conditional), transformers.models.detr.image_processing_detr_fast (conditional), transformers.models.detr.modeling_detr (top-level), transformers.models.diffllama.modeling_diffllama (top-level), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.modeling_distilbert (top-level), transformers.models.donut.image_processing_donut_fast (conditional), transformers.models.donut.modeling_donut_swin (top-level), transformers.models.dpr.modeling_dpr (top-level), transformers.models.dpt.image_processing_dpt (conditional), transformers.models.dpt.modeling_dpt (top-level), transformers.models.efficientnet.image_processing_efficientnet_fast (conditional), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.emu3.modeling_emu3 (top-level), transformers.models.encodec.modeling_encodec (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.esm.modeling_esmfold (top-level), transformers.models.esm.openfold_utils.chunk_utils (top-level), transformers.models.esm.openfold_utils.tensor_utils (top-level), transformers.models.esm.openfold_utils.data_transforms (top-level), transformers.models.esm.openfold_utils.feats (top-level), transformers.models.esm.openfold_utils.rigid_utils (top-level), transformers.models.esm.openfold_utils.loss (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.falcon_mamba.modeling_falcon_mamba (top-level), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level), transformers.models.fastspeech2_conformer.modeling_fastspeech2_conformer (top-level), transformers.models.flaubert.modeling_flaubert (top-level), transformers.models.flava.image_processing_flava_fast (conditional), transformers.models.flava.modeling_flava (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.fsmt.modeling_fsmt (top-level), transformers.models.funnel.modeling_funnel (top-level), transformers.models.fuyu.image_processing_fuyu (delayed, conditional), transformers.models.fuyu.modeling_fuyu (top-level), transformers.models.fuyu.processing_fuyu (conditional), transformers.models.gemma.modeling_gemma (top-level), transformers.models.gemma2.modeling_gemma2 (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.gemma3.image_processing_gemma3_fast (conditional), transformers.models.gemma3.modeling_gemma3 (top-level), transformers.models.git.modeling_git (top-level), transformers.models.glm.modeling_glm (top-level), transformers.models.glm4.modeling_glm4 (top-level), transformers.models.glpn.image_processing_glpn (conditional), transformers.models.glpn.modeling_glpn (top-level), transformers.models.got_ocr2.image_processing_got_ocr2_fast (conditional), transformers.models.got_ocr2.modeling_got_ocr2 (top-level), transformers.models.gpt2.configuration_gpt2 (delayed, conditional), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.configuration_gpt_neo (delayed, conditional), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gpt_neox.modeling_gpt_neox (top-level), transformers.models.gpt_neox_japanese.modeling_gpt_neox_japanese (top-level), transformers.models.gpt_sw3.tokenization_gpt_sw3 (conditional), transformers.models.gptj.configuration_gptj (delayed, conditional), transformers.models.gptj.modeling_gptj (top-level), transformers.models.granite.modeling_granite (top-level), transformers.models.granite_speech.feature_extraction_granite_speech (conditional), transformers.models.granite_speech.modeling_granite_speech (top-level), transformers.models.granite_speech.processing_granite_speech (conditional), transformers.models.granitemoe.modeling_granitemoe (top-level), transformers.models.granitemoehybrid.modeling_granitemoehybrid (top-level), transformers.models.granitemoeshared.modeling_granitemoeshared (top-level), transformers.models.grounding_dino.image_processing_grounding_dino (delayed, conditional), transformers.models.grounding_dino.modeling_grounding_dino (top-level), transformers.models.grounding_dino.image_processing_grounding_dino_fast (conditional), transformers.models.grounding_dino.processing_grounding_dino (conditional), transformers.models.groupvit.modeling_groupvit (top-level), transformers.models.helium.modeling_helium (top-level), transformers.models.hgnet_v2.modeling_hgnet_v2 (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.hubert.modeling_hubert (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.ibert.quant_modules (top-level), transformers.models.idefics.image_processing_idefics (delayed, conditional), transformers.models.idefics.modeling_idefics (top-level), transformers.models.idefics.perceiver (top-level), transformers.models.idefics.vision (top-level), transformers.models.idefics.processing_idefics (conditional), transformers.models.idefics2.modeling_idefics2 (top-level), transformers.models.idefics3.modeling_idefics3 (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.informer.modeling_informer (top-level), transformers.models.instructblip.modeling_instructblip (top-level), transformers.models.instructblipvideo.modeling_instructblipvideo (top-level), transformers.models.instructblipvideo.video_processing_instructblipvideo (conditional), transformers.models.internvl.modeling_internvl (top-level), transformers.models.janus.modeling_janus (top-level), transformers.models.jetmoe.modeling_jetmoe (top-level), transformers.models.kosmos2.modeling_kosmos2 (top-level), transformers.models.layoutlm.configuration_layoutlm (delayed), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.image_processing_layoutlmv2_fast (conditional), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.image_processing_layoutlmv3_fast (conditional), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.image_processing_levit_fast (conditional), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.llava.image_processing_llava_fast (conditional), transformers.models.llava.modeling_llava (top-level), transformers.models.llava_next.image_processing_llava_next_fast (conditional), transformers.models.llava_next.modeling_llava_next (top-level), transformers.models.llava_next_video.modeling_llava_next_video (top-level), transformers.models.llava_onevision.image_processing_llava_onevision_fast (top-level), transformers.models.llava_onevision.modeling_llava_onevision (top-level), transformers.models.longformer.configuration_longformer (delayed), transformers.models.longformer.modeling_longformer (top-level), transformers.models.longt5.modeling_longt5 (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.lxmert.modeling_lxmert (top-level), transformers.models.m2m_100.configuration_m2m_100 (delayed, conditional), transformers.models.m2m_100.modeling_m2m_100 (top-level), transformers.models.mamba.modeling_mamba (top-level), transformers.models.mamba2.modeling_mamba2 (top-level), transformers.models.marian.configuration_marian (delayed, conditional), transformers.models.marian.modeling_marian (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.mask2former.image_processing_mask2former (conditional), transformers.models.mask2former.modeling_mask2former (top-level), transformers.models.swin.modeling_swin (top-level), transformers.models.maskformer.image_processing_maskformer (conditional), transformers.models.maskformer.modeling_maskformer (top-level), transformers.models.maskformer.modeling_maskformer_swin (top-level), transformers.models.mbart.configuration_mbart (delayed, conditional), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mgp_str.modeling_mgp_str (top-level), transformers.models.mgp_str.processing_mgp_str (conditional), transformers.models.mimi.modeling_mimi (top-level), transformers.models.mistral.modeling_mistral (top-level), transformers.models.mistral3.modeling_mistral3 (top-level), transformers.models.mixtral.modeling_mixtral (top-level), transformers.models.mlcd.modeling_mlcd (top-level), transformers.models.mllama.modeling_mllama (top-level), transformers.models.mobilebert.modeling_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.image_processing_mobilenet_v2 (conditional), transformers.models.mobilenet_v2.image_processing_mobilenet_v2_fast (conditional), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (top-level), transformers.models.mobilevit.image_processing_mobilevit (conditional), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.modernbert.modeling_modernbert (top-level), transformers.models.moonshine.modeling_moonshine (top-level), transformers.models.moshi.modeling_moshi (top-level), transformers.models.mpnet.modeling_mpnet (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.t5.modeling_t5 (top-level), transformers.models.mt5.modeling_mt5 (top-level), transformers.models.musicgen.modeling_musicgen (top-level), transformers.models.musicgen_melody.modeling_musicgen_melody (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nemotron.modeling_nemotron (top-level), transformers.models.nllb_moe.modeling_nllb_moe (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.olmo.modeling_olmo (top-level), transformers.models.olmo2.modeling_olmo2 (top-level), transformers.models.olmoe.modeling_olmoe (top-level), transformers.models.omdet_turbo.modeling_omdet_turbo (top-level), transformers.models.omdet_turbo.processing_omdet_turbo (conditional), transformers.models.oneformer.image_processing_oneformer (conditional), transformers.models.oneformer.modeling_oneformer (top-level), transformers.models.oneformer.processing_oneformer (conditional), transformers.models.openai.modeling_openai (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.owlv2.image_processing_owlv2 (conditional), transformers.models.owlv2.modeling_owlv2 (top-level), transformers.models.owlv2.processing_owlv2 (delayed, conditional), transformers.models.owlvit.modeling_owlvit (top-level), transformers.models.owlvit.image_processing_owlvit (conditional), transformers.models.owlvit.image_processing_owlvit_fast (conditional), transformers.models.owlvit.processing_owlvit (delayed, conditional), transformers.models.paligemma.modeling_paligemma (top-level), transformers.models.patchtsmixer.modeling_patchtsmixer (top-level), transformers.models.patchtst.modeling_patchtst (top-level), transformers.models.pegasus.modeling_pegasus (top-level), transformers.models.pegasus_x.modeling_pegasus_x (top-level), transformers.models.perceiver.image_processing_perceiver_fast (conditional), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.persimmon.modeling_persimmon (top-level), transformers.models.phi.modeling_phi (top-level), transformers.models.phi3.modeling_phi3 (top-level), transformers.models.phi4_multimodal.feature_extraction_phi4_multimodal (conditional), transformers.models.phi4_multimodal.image_processing_phi4_multimodal_fast (top-level), transformers.models.phi4_multimodal.modeling_phi4_multimodal (top-level), transformers.models.phimoe.modeling_phimoe (top-level), transformers.models.pix2struct.image_processing_pix2struct (conditional), transformers.models.pix2struct.modeling_pix2struct (top-level), transformers.models.pixtral.image_processing_pixtral_fast (conditional), transformers.models.pixtral.modeling_pixtral (top-level), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.image_processing_poolformer_fast (conditional), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.pop2piano.modeling_pop2piano (top-level), transformers.models.prophetnet.modeling_prophetnet (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.qwen2.modeling_qwen2 (top-level), transformers.models.qwen2_5_vl.modeling_qwen2_5_vl (top-level), transformers.models.qwen2_audio.modeling_qwen2_audio (top-level), transformers.models.qwen2_moe.modeling_qwen2_moe (top-level), transformers.models.qwen2_vl.image_processing_qwen2_vl_fast (conditional), transformers.models.qwen2_vl.modeling_qwen2_vl (top-level), transformers.models.rag.modeling_rag (top-level), transformers.models.recurrent_gemma.modeling_recurrent_gemma (top-level), transformers.models.reformer.modeling_reformer (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.rt_detr.image_processing_rt_detr (delayed, conditional), transformers.models.rt_detr.image_processing_rt_detr_fast (conditional), transformers.models.rt_detr.modeling_rt_detr (top-level), transformers.models.rt_detr.modeling_rt_detr_resnet (top-level), transformers.models.rt_detr_v2.modeling_rt_detr_v2 (top-level), transformers.models.rwkv.modeling_rwkv (top-level), transformers.models.sam.image_processing_sam (conditional), transformers.models.sam.modeling_sam (top-level), transformers.models.sam.processing_sam (conditional), transformers.models.sam_hq.modeling_sam_hq (top-level), transformers.models.sam_hq.processing_samhq (conditional), transformers.models.seamless_m4t.feature_extraction_seamless_m4t (conditional), transformers.models.seamless_m4t.modeling_seamless_m4t (top-level), transformers.models.seamless_m4t_v2.modeling_seamless_m4t_v2 (top-level), transformers.models.segformer.image_processing_segformer (conditional), transformers.models.segformer.modeling_segformer (top-level), transformers.models.seggpt.image_processing_seggpt (conditional), transformers.models.seggpt.modeling_seggpt (top-level), transformers.models.sew.modeling_sew (top-level), transformers.models.sew_d.modeling_sew_d (top-level), transformers.models.siglip2.image_processing_siglip2_fast (top-level), transformers.models.siglip2.modeling_siglip2 (top-level), transformers.models.smolvlm.modeling_smolvlm (top-level), transformers.models.smolvlm.video_processing_smolvlm (conditional), transformers.models.speech_encoder_decoder.modeling_speech_encoder_decoder (top-level), transformers.models.speech_to_text.feature_extraction_speech_to_text (conditional), transformers.models.speech_to_text.modeling_speech_to_text (top-level), transformers.models.speecht5.modeling_speecht5 (top-level), transformers.models.splinter.modeling_splinter (top-level), transformers.models.squeezebert.modeling_squeezebert (top-level), transformers.models.stablelm.modeling_stablelm (top-level), transformers.models.starcoder2.modeling_starcoder2 (top-level), transformers.models.superpoint.image_processing_superpoint (conditional), transformers.models.superpoint.modeling_superpoint (top-level), transformers.models.superglue.image_processing_superglue (conditional), transformers.models.superglue.modeling_superglue (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.swin2sr.image_processing_swin2sr_fast (conditional), transformers.models.swin2sr.modeling_swin2sr (top-level), transformers.models.swinv2.modeling_swinv2 (top-level), transformers.models.switch_transformers.modeling_switch_transformers (top-level), transformers.models.table_transformer.modeling_table_transformer (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.textnet.modeling_textnet (top-level), transformers.models.time_series_transformer.modeling_time_series_transformer (top-level), transformers.models.timesfm.modeling_timesfm (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.timm_wrapper.modeling_timm_wrapper (top-level), transformers.models.trocr.modeling_trocr (top-level), transformers.models.tvp.modeling_tvp (top-level), transformers.models.udop.modeling_udop (top-level), transformers.models.umt5.modeling_umt5 (top-level), transformers.models.unispeech.modeling_unispeech (top-level), transformers.models.unispeech_sat.modeling_unispeech_sat (top-level), transformers.models.univnet.modeling_univnet (top-level), transformers.models.upernet.modeling_upernet (top-level), transformers.models.video_llava.modeling_video_llava (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vilt.image_processing_vilt_fast (conditional), transformers.models.vilt.modeling_vilt (top-level), transformers.models.vipllava.modeling_vipllava (top-level), transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder (delayed), transformers.models.vision_encoder_decoder.modeling_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_vision_text_dual_encoder (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_mae.modeling_vit_mae (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.vitdet.modeling_vitdet (top-level), transformers.models.vitmatte.image_processing_vitmatte_fast (conditional), transformers.models.vitmatte.modeling_vitmatte (top-level), transformers.models.vitpose.image_processing_vitpose (conditional), transformers.models.vitpose.modeling_vitpose (top-level), transformers.models.vitpose_backbone.modeling_vitpose_backbone (top-level), transformers.models.vits.modeling_vits (top-level), transformers.models.vivit.modeling_vivit (top-level), transformers.models.wav2vec2.modeling_wav2vec2 (top-level), transformers.models.wav2vec2.tokenization_wav2vec2 (conditional), transformers.models.wav2vec2_bert.modeling_wav2vec2_bert (top-level), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (top-level), transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (conditional), transformers.models.wavlm.modeling_wavlm (top-level), transformers.models.whisper.feature_extraction_whisper (conditional), transformers.models.whisper.modeling_whisper (top-level), transformers.models.whisper.generation_whisper (top-level), transformers.models.x_clip.modeling_x_clip (top-level), transformers.models.xglm.modeling_xglm (top-level), transformers.models.xlm.modeling_xlm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xlnet.modeling_xlnet (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yolos.image_processing_yolos (delayed, conditional), transformers.models.yolos.image_processing_yolos_fast (conditional), transformers.models.yolos.modeling_yolos (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), transformers.models.zamba2.modeling_zamba2 (top-level), transformers.models.zoedepth.modeling_zoedepth (top-level), transformers.models.zoedepth.image_processing_zoedepth (conditional), transformers.pipelines.base (conditional), transformers.pipelines.pt_utils (top-level), transformers.pipelines.audio_classification (delayed, conditional), transformers.pipelines.automatic_speech_recognition (conditional), transformers.pipelines.question_answering (conditional), transformers.pipelines.document_question_answering (conditional), transformers.pipelines.fill_mask (conditional), transformers.pipelines.image_classification (conditional), transformers.pipelines.image_to_text (conditional), transformers.pipelines.mask_generation (conditional), transformers.pipelines.object_detection (conditional), transformers.pipelines.table_question_answering (conditional), transformers.pipelines.text_generation (conditional), transformers.pipelines.text_to_audio (conditional), transformers.pipelines.token_classification (conditional), transformers.pipelines.zero_shot_image_classification (conditional), transformers.pipelines.zero_shot_object_detection (conditional), transformers.pipelines (conditional), transformers.data.datasets.glue (top-level), transformers.data.datasets.language_modeling (top-level), transformers.data.datasets.squad (top-level), transformers.model_debugging_utils (conditional), transformers.trainer_seq2seq (top-level)
missing module named gguf - imported by transformers.modeling_gguf_pytorch_utils (delayed, conditional)
missing module named transformers.models.auto.TFAutoModelForTokenClassification - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.TFAutoModelForSequenceClassification - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.TFAutoModelForSeq2SeqLM - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.TFAutoModelForSemanticSegmentation - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.TFAutoModelForQuestionAnswering - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.TFAutoModelForMultipleChoice - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.TFAutoModelForMaskedLM - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.TFAutoModelForCausalLM - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.TFAutoModel - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForVision2Seq - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForTokenClassification - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForSpeechSeq2Seq - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForSequenceClassification - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForSeq2SeqLM - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional), transformers.models.blip_2.modeling_blip_2 (top-level), transformers.models.instructblip.modeling_instructblip (top-level), transformers.models.instructblipvideo.modeling_instructblipvideo (top-level)
missing module named transformers.models.auto.AutoModelForSemanticSegmentation - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForQuestionAnswering - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForObjectDetection - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForMultipleChoice - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForMaskedLM - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForMaskedImageModeling - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForImageSegmentation - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForImageClassification - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForCausalLM - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional), transformers.models.blip_2.modeling_blip_2 (top-level), transformers.models.granite_speech.modeling_granite_speech (top-level), transformers.models.instructblip.modeling_instructblip (top-level), transformers.models.instructblipvideo.modeling_instructblipvideo (top-level), transformers.models.qwen2_audio.modeling_qwen2_audio (top-level)
missing module named transformers.models.auto.AutoModel - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional), transformers.models.aria.modeling_aria (top-level), transformers.models.aya_vision.modeling_aya_vision (top-level), transformers.models.bark.modeling_bark (top-level), transformers.models.csm.modeling_csm (top-level), transformers.models.depth_pro.modeling_depth_pro (top-level), transformers.models.gemma3.modeling_gemma3 (top-level), transformers.models.got_ocr2.modeling_got_ocr2 (top-level), transformers.models.granite_speech.modeling_granite_speech (top-level), transformers.models.grounding_dino.modeling_grounding_dino (top-level), transformers.models.idefics2.modeling_idefics2 (top-level), transformers.models.idefics3.modeling_idefics3 (top-level), transformers.models.instructblip.modeling_instructblip (top-level), transformers.models.instructblipvideo.modeling_instructblipvideo (top-level), transformers.models.internvl.modeling_internvl (top-level), transformers.models.janus.modeling_janus (top-level), transformers.models.llava.modeling_llava (top-level), transformers.models.llava_next.modeling_llava_next (top-level), transformers.models.llava_next_video.modeling_llava_next_video (top-level), transformers.models.llava_onevision.modeling_llava_onevision (top-level), transformers.models.mistral3.modeling_mistral3 (top-level), transformers.models.omdet_turbo.modeling_omdet_turbo (top-level), transformers.models.paligemma.modeling_paligemma (top-level), transformers.models.qwen2_audio.modeling_qwen2_audio (top-level), transformers.models.smolvlm.modeling_smolvlm (top-level), transformers.models.video_llava.modeling_video_llava (top-level), transformers.models.vipllava.modeling_vipllava (top-level)
missing module named timm - imported by transformers.models.timm_backbone.modeling_timm_backbone (conditional), transformers.models.conditional_detr.modeling_conditional_detr (conditional), transformers.models.deformable_detr.modeling_deformable_detr (conditional), transformers.models.detr.modeling_detr (conditional), transformers.models.grounding_dino.modeling_grounding_dino (conditional), transformers.models.table_transformer.modeling_table_transformer (conditional), transformers.models.timm_wrapper.modeling_timm_wrapper (conditional)
missing module named 'torch.utils' - imported by huggingface_hub.serialization._torch (delayed, optional), transformers.utils.generic (delayed, conditional), transformers.modeling_utils (top-level), transformers.trainer_pt_utils (top-level), transformers.models.roformer.modeling_roformer (top-level), transformers.models.llama.modeling_llama (top-level), transformers.integrations.integration_utils (delayed, conditional, optional), transformers.trainer (top-level), transformers.models.bert.modeling_bert (top-level), transformers.data.processors.utils (delayed, conditional), transformers.data.processors.squad (conditional), transformers.integrations.tpu (top-level), transformers.models.align.modeling_align (top-level), transformers.models.altclip.modeling_altclip (top-level), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.autoformer.modeling_autoformer (top-level), transformers.models.jamba.modeling_jamba (top-level), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.bert_generation.modeling_bert_generation (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.blenderbot.modeling_blenderbot (top-level), transformers.models.blenderbot_small.modeling_blenderbot_small (top-level), transformers.models.blip.modeling_blip (top-level), transformers.models.blip.modeling_blip_text (top-level), transformers.models.blip_2.modeling_blip_2 (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.bridgetower.modeling_bridgetower (top-level), transformers.models.bros.modeling_bros (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.chameleon.modeling_chameleon (top-level), transformers.models.chinese_clip.modeling_chinese_clip (top-level), transformers.models.clipseg.modeling_clipseg (top-level), transformers.models.clvp.modeling_clvp (top-level), transformers.models.codegen.modeling_codegen (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.cpmant.modeling_cpmant (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.dbrx.modeling_dbrx (top-level), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.decision_transformer.modeling_decision_transformer (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.deprecated.deta.modeling_deta (delayed), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.mctct.modeling_mctct (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.retribert.modeling_retribert (top-level), transformers.models.deprecated.trajectory_transformer.modeling_trajectory_transformer (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level), transformers.models.deprecated.xlm_prophetnet.modeling_xlm_prophetnet (top-level), transformers.models.depth_anything.modeling_depth_anything (top-level), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.donut.modeling_donut_swin (top-level), transformers.models.dpt.modeling_dpt (top-level), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.encodec.modeling_encodec (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.falcon_mamba.modeling_falcon_mamba (top-level), transformers.models.flava.modeling_flava (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.fuyu.modeling_fuyu (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.git.modeling_git (top-level), transformers.models.glpn.modeling_glpn (top-level), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gpt_neox_japanese.modeling_gpt_neox_japanese (top-level), transformers.models.gptj.modeling_gptj (top-level), transformers.models.groupvit.modeling_groupvit (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.idefics.modeling_idefics (top-level), transformers.models.idefics.vision (top-level), transformers.models.idefics2.modeling_idefics2 (top-level), transformers.models.idefics3.modeling_idefics3 (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.instructblip.modeling_instructblip (top-level), transformers.models.jetmoe.modeling_jetmoe (top-level), transformers.models.kosmos2.modeling_kosmos2 (top-level), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.llava.modeling_llava (top-level), transformers.models.longformer.modeling_longformer (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.mamba.modeling_mamba (top-level), transformers.models.mamba2.modeling_mamba2 (top-level), transformers.models.marian.modeling_marian (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.swin.modeling_swin (top-level), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mgp_str.modeling_mgp_str (top-level), transformers.models.mimi.modeling_mimi (top-level), transformers.models.mllama.modeling_mllama (top-level), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.moshi.modeling_moshi (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nemotron.modeling_nemotron (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.olmoe.modeling_olmoe (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.owlv2.modeling_owlv2 (top-level), transformers.models.owlvit.modeling_owlvit (top-level), transformers.models.paligemma.modeling_paligemma (top-level), transformers.models.pegasus.modeling_pegasus (top-level), transformers.models.pegasus_x.modeling_pegasus_x (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.persimmon.modeling_persimmon (top-level), transformers.models.phimoe.modeling_phimoe (top-level), transformers.models.pix2struct.modeling_pix2struct (top-level), transformers.models.pixtral.modeling_pixtral (top-level), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.prophetnet.modeling_prophetnet (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.qwen2_audio.modeling_qwen2_audio (top-level), transformers.models.qwen2_moe.modeling_qwen2_moe (top-level), transformers.models.qwen2_vl.modeling_qwen2_vl (top-level), transformers.models.recurrent_gemma.modeling_recurrent_gemma (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.rwkv.modeling_rwkv (top-level), transformers.models.sam.modeling_sam (top-level), transformers.models.seamless_m4t.modeling_seamless_m4t (top-level), transformers.models.seamless_m4t_v2.modeling_seamless_m4t_v2 (top-level), transformers.models.segformer.modeling_segformer (top-level), transformers.models.seggpt.modeling_seggpt (top-level), transformers.models.sew.modeling_sew (top-level), transformers.models.sew_d.modeling_sew_d (top-level), transformers.models.speecht5.modeling_speecht5 (top-level), transformers.models.splinter.modeling_splinter (top-level), transformers.models.stablelm.modeling_stablelm (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.swin2sr.modeling_swin2sr (top-level), transformers.models.swinv2.modeling_swinv2 (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.tvp.modeling_tvp (top-level), transformers.models.univnet.modeling_univnet (top-level), transformers.models.video_llava.modeling_video_llava (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vilt.modeling_vilt (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_mae.modeling_vit_mae (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.vitdet.modeling_vitdet (top-level), transformers.models.vitpose.modeling_vitpose (top-level), transformers.models.vitpose_backbone.modeling_vitpose_backbone (top-level), transformers.models.vits.modeling_vits (top-level), transformers.models.vivit.modeling_vivit (top-level), transformers.models.wav2vec2.modeling_wav2vec2 (top-level), transformers.models.whisper.modeling_whisper (top-level), transformers.models.x_clip.modeling_x_clip (top-level), transformers.models.xglm.modeling_xglm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yolos.modeling_yolos (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), transformers.models.zoedepth.modeling_zoedepth (top-level), transformers.pipelines.base (conditional), transformers.pipelines.pt_utils (top-level), transformers.pipelines.question_answering (conditional), transformers.data.datasets.glue (top-level), transformers.data.datasets.language_modeling (top-level), transformers.data.datasets.squad (top-level), transformers.trainer_seq2seq (top-level)
missing module named mlx - imported by transformers.utils.generic (delayed)
missing module named cython - imported by pydantic.v1.version (optional)
missing module named email_validator - imported by pydantic.networks (delayed, conditional, optional), pydantic.v1.networks (delayed, conditional, optional), pydantic.v1._hypothesis_plugin (optional)
missing module named toml - imported by huggingface_hub.fastai_utils (delayed, optional), pydantic.v1.mypy (delayed, conditional, optional)
missing module named 'mypy.version' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.util' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.typevars' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.types' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.server' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.semanal' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugins' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugin' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.options' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.nodes' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.errorcodes' - imported by pydantic.v1.mypy (top-level)
missing module named hypothesis - imported by pydantic.v1._hypothesis_plugin (top-level)
missing module named 'mypy.typeops' - imported by pydantic.mypy (top-level)
missing module named 'mypy.type_visitor' - imported by pydantic.mypy (top-level)
missing module named 'mypy.state' - imported by pydantic.mypy (top-level)
missing module named 'mypy.expandtype' - imported by pydantic.mypy (top-level)
missing module named mypy - imported by pydantic.mypy (top-level)
missing module named eval_type_backport - imported by pydantic._internal._typing_extra (delayed, optional)
missing module named 'rich.pretty' - imported by pydantic._internal._core_utils (delayed)
missing module named rich - imported by pydantic._internal._core_utils (conditional)
missing module named pydantic.PydanticUserError - imported by pydantic (top-level), pydantic.root_model (top-level)
missing module named pydantic.PydanticSchemaGenerationError - imported by pydantic (delayed), pydantic.functional_validators (delayed, conditional)
missing module named pydantic.BaseModel - imported by pydantic (conditional), pydantic._internal._typing_extra (conditional), pydantic._internal._import_utils (delayed, conditional), pydantic._internal._core_utils (delayed), pydantic.deprecated.copy_internals (delayed, conditional), openai.resources.beta.realtime.realtime (top-level), huggingface_hub._webhooks_payload (conditional)
missing module named 'google.colab' - imported by huggingface_hub.utils._auth (delayed, optional)
missing module named google - imported by huggingface_hub.utils._auth (delayed, optional)
missing module named hf_xet - imported by huggingface_hub.file_download (delayed, optional), huggingface_hub._commit_api (delayed)
missing module named hf_transfer - imported by huggingface_hub.file_download (delayed, conditional, optional), huggingface_hub.lfs (delayed, optional)
missing module named 'torch_xla.core' - imported by huggingface_hub.serialization._torch (delayed, conditional, optional), transformers.trainer_pt_utils (delayed, conditional), transformers.training_args (conditional), transformers.trainer (conditional)
missing module named torch_xla - imported by huggingface_hub.serialization._torch (delayed, conditional), transformers.utils.import_utils (delayed), transformers.pytorch_utils (delayed, conditional), transformers.trainer (conditional)
missing module named 'torch.distributed' - imported by huggingface_hub.serialization._torch (delayed, optional), transformers.modeling_utils (top-level), transformers.pytorch_utils (delayed, conditional), transformers.training_args (conditional), transformers.trainer_pt_utils (top-level), transformers.generation.utils (top-level), transformers.integrations.fsdp (delayed), transformers.integrations.tensor_parallel (top-level), transformers.trainer (top-level), transformers.model_debugging_utils (conditional), transformers.trainer_seq2seq (top-level)
missing module named jinja2 - imported by huggingface_hub.repocard (delayed, conditional), transformers.utils.chat_template_utils (conditional), fsspec.implementations.reference (delayed, conditional)
missing module named tf_keras - imported by huggingface_hub.keras_mixin (conditional, optional), transformers.activations_tf (optional), transformers.modeling_tf_utils (optional)
missing module named 'mcp.client' - imported by huggingface_hub.inference._mcp.mcp_client (delayed, conditional)
missing module named mcp - imported by huggingface_hub.inference._mcp.utils (conditional), huggingface_hub.inference._mcp.mcp_client (delayed, conditional)
missing module named aiohttp - imported by huggingface_hub.inference._common (delayed, conditional), huggingface_hub.inference._generated._async_client (conditional), fsspec.implementations.http (top-level)
missing module named ujson - imported by fsspec.implementations.cache_metadata (optional), fsspec.implementations.reference (optional)
missing module named lz4 - imported by fsspec.compression (optional)
missing module named snappy - imported by fsspec.compression (delayed, optional)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named distributed - imported by fsspec.transaction (delayed)
missing module named 'pyarrow.parquet' - imported by fsspec.parquet (delayed)
missing module named fastparquet - imported by fsspec.parquet (delayed)
missing module named requests_kerberos - imported by fsspec.implementations.webhdfs (delayed, conditional)
missing module named smbprotocol - imported by fsspec.implementations.smb (top-level)
missing module named smbclient - imported by fsspec.implementations.smb (top-level)
missing module named paramiko - imported by fsspec.implementations.sftp (top-level)
missing module named kerchunk - imported by fsspec.implementations.reference (delayed)
missing module named 'libarchive.ffi' - imported by fsspec.implementations.libarchive (top-level)
missing module named libarchive - imported by fsspec.implementations.libarchive (top-level)
missing module named yarl - imported by fsspec.implementations.http (top-level), fsspec.implementations.http_sync (optional)
missing module named pygit2 - imported by fsspec.implementations.git (top-level)
missing module named 'distributed.worker' - imported by fsspec.implementations.dask (top-level)
missing module named 'distributed.client' - imported by fsspec.implementations.dask (top-level)
missing module named dask - imported by fsspec.implementations.dask (top-level)
missing module named 'pyarrow.fs' - imported by fsspec.implementations.arrow (delayed)
missing module named pyarrow - imported by fsspec.implementations.arrow (delayed)
missing module named panel - imported by fsspec.gui (top-level)
missing module named fuse - imported by fsspec.fuse (top-level)
missing module named pytest - imported by fsspec.conftest (top-level)
missing module named fastai - imported by huggingface_hub.fastai_utils (delayed)
missing module named 'fastapi.responses' - imported by huggingface_hub._oauth (delayed, optional), huggingface_hub._webhooks_server (conditional)
missing module named fastapi - imported by huggingface_hub._oauth (delayed, conditional, optional), huggingface_hub._webhooks_server (conditional)
missing module named gradio - imported by huggingface_hub._webhooks_server (delayed, conditional)
missing module named tensorboardX - imported by huggingface_hub._tensorboard_logger (conditional, optional), transformers.integrations.integration_utils (delayed, conditional, optional)
missing module named 'starlette.datastructures' - imported by huggingface_hub._oauth (delayed, optional)
missing module named 'authlib.integrations' - imported by huggingface_hub._oauth (delayed, optional)
missing module named authlib - imported by huggingface_hub._oauth (delayed, optional)
missing module named starlette - imported by huggingface_hub._oauth (delayed, optional)
missing module named 'ipywidgets.widgets' - imported by huggingface_hub._login (delayed, optional)
missing module named 'InquirerPy.separator' - imported by huggingface_hub.commands.delete_cache (optional)
missing module named 'InquirerPy.base' - imported by huggingface_hub.commands.delete_cache (optional)
missing module named InquirerPy - imported by huggingface_hub.commands.delete_cache (optional)
missing module named 'jax.random' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named 'flax.traverse_util' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named 'flax.serialization' - imported by transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level)
missing module named 'flax.core' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named msgpack - imported by transformers.modeling_flax_utils (top-level)
missing module named 'jax.numpy' - imported by transformers.utils.generic (delayed, conditional), transformers.feature_extraction_utils (delayed, conditional), transformers.image_transforms (conditional), transformers.tokenization_utils_base (delayed, conditional), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_outputs (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), safetensors.flax (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.generation.flax_logits_process (top-level), transformers.generation.flax_utils (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.conditional_detr.image_processing_conditional_detr (delayed, conditional), transformers.models.deformable_detr.image_processing_deformable_detr (delayed, conditional), transformers.models.deprecated.deta.image_processing_deta (delayed, conditional), transformers.models.deprecated.jukebox.tokenization_jukebox (delayed, conditional), transformers.models.detr.image_processing_detr (delayed, conditional), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.grounding_dino.image_processing_grounding_dino (delayed, conditional), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.mt5.modeling_flax_mt5 (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.owlv2.processing_owlv2 (delayed, conditional), transformers.models.owlvit.processing_owlvit (delayed, conditional), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.rt_detr.image_processing_rt_detr (delayed, conditional), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.wav2vec2.tokenization_wav2vec2 (conditional), transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (conditional), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), transformers.models.yolos.image_processing_yolos (delayed, conditional)
missing module named jax - imported by transformers.utils.generic (delayed, conditional), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), safetensors.flax (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.generation.flax_logits_process (top-level), transformers.generation.flax_utils (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named 'flax.linen' - imported by transformers.modeling_flax_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named 'tensorflow.keras' - imported by transformers.optimization_tf (optional)
missing module named 'tf_keras.optimizers' - imported by transformers.optimization_tf (optional)
missing module named datasets - imported by transformers.modeling_tf_utils (delayed), transformers.modelcard (delayed, conditional), transformers.trainer (conditional), transformers.models.rag.retrieval_rag (conditional), transformers.trainer_seq2seq (conditional)
missing module named keras - imported by transformers.activations_tf (optional), transformers.modeling_tf_utils (optional)
missing module named h5py - imported by transformers.modeling_tf_utils (top-level)
missing module named 'deepspeed.utils' - imported by transformers.integrations.deepspeed (delayed)
missing module named deepspeed - imported by transformers.integrations.deepspeed (delayed, conditional), transformers.modeling_utils (delayed, conditional), transformers.models.distilbert.modeling_distilbert (delayed, conditional), transformers.models.esm.modeling_esmfold (delayed, conditional, optional), transformers.models.fsmt.modeling_fsmt (delayed, conditional), transformers.models.hubert.modeling_hubert (delayed, conditional), transformers.models.seamless_m4t.modeling_seamless_m4t (delayed, conditional), transformers.models.sew.modeling_sew (delayed, conditional), transformers.models.sew_d.modeling_sew_d (delayed, conditional), transformers.models.speecht5.modeling_speecht5 (delayed, conditional), transformers.models.unispeech.modeling_unispeech (delayed, conditional), transformers.models.unispeech_sat.modeling_unispeech_sat (delayed, conditional), transformers.models.wav2vec2.modeling_wav2vec2 (delayed, conditional), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (delayed, conditional), transformers.models.wavlm.modeling_wavlm (delayed, conditional)
missing module named 'accelerate.utils' - imported by transformers.trainer_utils (delayed, conditional), transformers.training_args (delayed, conditional), transformers.integrations.deepspeed (delayed, conditional), transformers.loss.loss_for_object_detection (conditional), transformers.quantizers.quantizer_bnb_4bit (delayed, conditional), transformers.quantizers.quantizer_quanto (delayed, conditional), transformers.quantizers.quantizer_quark (conditional), transformers.quantizers.quantizer_torchao (delayed, conditional), transformers.modeling_utils (conditional), transformers.integrations.bitsandbytes (conditional), transformers.integrations.integration_utils (delayed), transformers.trainer (delayed, conditional), transformers.integrations.peft (conditional), transformers.models.deprecated.deta.modeling_deta (conditional), transformers.models.mask2former.modeling_mask2former (conditional), transformers.models.maskformer.modeling_maskformer (conditional), transformers.models.oneformer.modeling_oneformer (conditional)
missing module named smdistributed - imported by transformers.trainer_pt_utils (conditional)
missing module named 'torch.optim' - imported by transformers.trainer_pt_utils (conditional), transformers.optimization (top-level), transformers.trainer (delayed, conditional)
missing module named 'torch_xla.runtime' - imported by transformers.trainer_utils (delayed, conditional), transformers.trainer_pt_utils (conditional), transformers.trainer (conditional)
missing module named 'peft.utils' - imported by transformers.trainer (delayed, conditional), transformers.integrations.peft (delayed)
missing module named wandb - imported by transformers.integrations.integration_utils (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named 'torch_xla.experimental' - imported by transformers.trainer (delayed, conditional, optional)
missing module named intel_extension_for_pytorch - imported by transformers.utils.import_utils (delayed, conditional), transformers.trainer (delayed)
missing module named 'ray.train' - imported by transformers.integrations.integration_utils (delayed), transformers.trainer (delayed, conditional)
missing module named schedulefree - imported by transformers.trainer (delayed, conditional)
missing module named 'torchao.prototype' - imported by transformers.trainer (delayed, conditional)
missing module named 'torchao.optim' - imported by transformers.trainer (delayed, conditional)
missing module named grokadamw - imported by transformers.trainer (delayed, conditional)
missing module named lomo_optim - imported by transformers.trainer (delayed, conditional)
missing module named apollo_torch - imported by transformers.trainer (delayed, conditional)
missing module named galore_torch - imported by transformers.trainer (delayed, conditional)
missing module named torchdistx - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'bitsandbytes.optim' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'apex.optimizers' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'torch_npu.optim' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'torch_xla.amp' - imported by transformers.trainer (delayed, conditional, optional)
missing module named bitsandbytes - imported by transformers.utils.import_utils (delayed), transformers.quantizers.quantizer_bnb_4bit (delayed), transformers.quantizers.quantizer_bnb_8bit (delayed), transformers.modeling_utils (delayed, conditional), transformers.integrations.bitsandbytes (delayed, conditional), transformers.trainer (delayed, conditional), transformers.models.rwkv.modeling_rwkv (delayed)
missing module named liger_kernel - imported by transformers.trainer (delayed, conditional)
missing module named optuna - imported by transformers.integrations.integration_utils (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named 'accelerate.data_loader' - imported by transformers.trainer (conditional)
missing module named 'accelerate.state' - imported by transformers.training_args (conditional), transformers.trainer (conditional)
missing module named accelerate - imported by transformers.integrations.aqlm (delayed), transformers.loss.loss_for_object_detection (conditional), transformers.quantizers.base (delayed), transformers.integrations.finegrained_fp8 (conditional), transformers.modeling_utils (conditional), transformers.integrations.bitnet (conditional), transformers.integrations.bitsandbytes (conditional), transformers.integrations.eetq (conditional), transformers.integrations.fbgemm_fp8 (conditional), transformers.integrations.higgs (delayed), transformers.trainer (conditional), transformers.integrations.peft (conditional), transformers.integrations.quanto (delayed), transformers.integrations.spqr (delayed, conditional), transformers.integrations.vptq (top-level), transformers.models.bark.modeling_bark (delayed, conditional), transformers.models.deprecated.deta.modeling_deta (conditional), transformers.models.mask2former.modeling_mask2former (conditional), transformers.models.maskformer.modeling_maskformer (conditional), transformers.models.oneformer.modeling_oneformer (conditional)
missing module named peft - imported by transformers.trainer (delayed, conditional), transformers.integrations.peft (delayed)
missing module named 'smdistributed.modelparallel' - imported by transformers.training_args (conditional), transformers.modeling_utils (conditional), transformers.trainer (conditional)
missing module named 'torch_xla.distributed' - imported by transformers.training_args (conditional), transformers.integrations.tpu (delayed, conditional), transformers.trainer (delayed, conditional, optional)
missing module named 'torch_xla.debug' - imported by transformers.trainer (conditional)
missing module named apex - imported by transformers.trainer (conditional)
missing module named 'optimum.bettertransformer' - imported by transformers.modeling_utils (delayed)
missing module named 'optimum.version' - imported by transformers.modeling_utils (delayed)
missing module named kernels - imported by transformers.integrations.hub_kernels (optional), transformers.modeling_utils (conditional)
missing module named 'accelerate.hooks' - imported by transformers.generation.utils (conditional), transformers.quantizers.quantizer_hqq (conditional), transformers.modeling_utils (conditional), transformers.integrations.bitsandbytes (conditional)
missing module named 'torchvision.transforms' - imported by transformers.image_processing_utils_fast (conditional), transformers.video_processing_utils (conditional), transformers.models.llama4.image_processing_llama4_fast (conditional), transformers.models.beit.image_processing_beit_fast (top-level), transformers.models.bridgetower.image_processing_bridgetower_fast (conditional), transformers.models.conditional_detr.image_processing_conditional_detr_fast (conditional), transformers.models.convnext.image_processing_convnext_fast (conditional), transformers.models.deformable_detr.image_processing_deformable_detr_fast (conditional), transformers.models.depth_pro.image_processing_depth_pro_fast (conditional), transformers.models.detr.image_processing_detr_fast (conditional), transformers.models.donut.image_processing_donut_fast (conditional), transformers.models.efficientnet.image_processing_efficientnet_fast (conditional), transformers.models.flava.image_processing_flava_fast (conditional), transformers.models.gemma3.image_processing_gemma3_fast (conditional), transformers.models.got_ocr2.image_processing_got_ocr2_fast (conditional), transformers.models.grounding_dino.image_processing_grounding_dino_fast (conditional), transformers.models.instructblipvideo.video_processing_instructblipvideo (conditional), transformers.models.layoutlmv2.image_processing_layoutlmv2_fast (conditional), transformers.models.layoutlmv3.image_processing_layoutlmv3_fast (conditional), transformers.models.levit.image_processing_levit_fast (conditional), transformers.models.llava.image_processing_llava_fast (conditional), transformers.models.llava_next.image_processing_llava_next_fast (conditional), transformers.models.llava_onevision.image_processing_llava_onevision_fast (conditional), transformers.models.perceiver.image_processing_perceiver_fast (conditional), transformers.models.phi4_multimodal.image_processing_phi4_multimodal_fast (conditional), transformers.models.pixtral.image_processing_pixtral_fast (conditional), transformers.models.poolformer.image_processing_poolformer_fast (conditional), transformers.models.qwen2_vl.image_processing_qwen2_vl_fast (conditional), transformers.models.rt_detr.image_processing_rt_detr_fast (conditional), transformers.models.siglip2.image_processing_siglip2_fast (conditional), transformers.models.smolvlm.video_processing_smolvlm (conditional), transformers.models.swin2sr.image_processing_swin2sr_fast (conditional), transformers.models.vilt.image_processing_vilt_fast (conditional), transformers.models.vitmatte.image_processing_vitmatte_fast (conditional), transformers.models.yolos.image_processing_yolos_fast (conditional)
missing module named einops - imported by transformers.integrations.npu_flash_attention (conditional), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level)
missing module named torch_npu - imported by transformers.utils.import_utils (delayed), transformers.modeling_flash_attention_utils (conditional), transformers.integrations.npu_flash_attention (conditional)
missing module named 'flash_attn.layers' - imported by transformers.modeling_flash_attention_utils (conditional), transformers.models.modernbert.modeling_modernbert (conditional)
missing module named flash_attn - imported by transformers.modeling_flash_attention_utils (conditional)
missing module named vptq - imported by transformers.quantizers.quantizer_vptq (delayed, conditional), transformers.integrations.vptq (top-level)
missing module named 'torchao.core' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_torchao (delayed, conditional)
missing module named 'torchao.quantization' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_torchao (delayed, conditional)
missing module named 'torchao.dtypes' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_torchao (delayed)
missing module named 'quark.torch' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_quark (delayed)
missing module named 'optimum.quanto' - imported by transformers.cache_utils (delayed, conditional), transformers.quantizers.quantizer_quanto (delayed, conditional), transformers.integrations.quanto (delayed, conditional)
missing module named 'hqq.core' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_hqq (delayed, conditional), transformers.integrations.hqq (delayed, conditional)
missing module named 'flute.utils' - imported by transformers.quantizers.quantizer_higgs (delayed)
missing module named flute - imported by transformers.quantizers.quantizer_higgs (delayed)
missing module named 'optimum.gptq' - imported by transformers.quantizers.quantizer_gptq (delayed)
missing module named 'triton.language' - imported by transformers.integrations.finegrained_fp8 (conditional)
missing module named triton - imported by transformers.integrations.finegrained_fp8 (conditional)
missing module named eetq - imported by transformers.quantizers.quantizer_eetq (delayed, optional), transformers.integrations.eetq (conditional)
missing module named 'compressed_tensors.quantization' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_compressed_tensors (delayed, conditional)
missing module named 'compressed_tensors.compressors' - imported by transformers.quantizers.quantizer_compressed_tensors (delayed)
missing module named 'auto_round.inference' - imported by transformers.quantizers.quantizer_auto_round (delayed, conditional)
missing module named auto_round - imported by transformers.quantizers.quantizer_auto_round (delayed)
missing module named 'scipy.optimize' - imported by transformers.loss.loss_rt_detr (conditional), transformers.loss.loss_deformable_detr (conditional), transformers.loss.loss_grounding_dino (conditional), transformers.models.deprecated.deta.modeling_deta (conditional), transformers.models.mask2former.modeling_mask2former (conditional), transformers.models.maskformer.modeling_maskformer (conditional), transformers.models.oneformer.modeling_oneformer (conditional)
missing module named scipy - imported by transformers.loss.loss_for_object_detection (conditional), transformers.models.fnet.modeling_fnet (conditional), transformers.models.owlv2.image_processing_owlv2 (conditional)
missing module named torchao - imported by transformers.modeling_utils (conditional), transformers.quantizers.quantizer_torchao (delayed, conditional)
missing module named 'torch.distributions' - imported by transformers.modeling_utils (top-level), transformers.time_series_utils (top-level)
missing module named 'torch.export' - imported by transformers.integrations.executorch (delayed)
missing module named tensorflow_text - imported by transformers.models.bert.tokenization_bert_tf (top-level), transformers.models.bert_generation.modeling_bert_generation (delayed, optional), transformers.models.gpt2.tokenization_gpt2_tf (top-level)
missing module named sentencepiece_model_pb2 - imported by tokenizers.implementations.sentencepiece_unigram (delayed, optional)
missing module named tf2onnx - imported by transformers.onnx.convert (delayed)
missing module named onnx - imported by transformers.onnx.convert (delayed)
missing module named 'torch.onnx' - imported by transformers.onnx.convert (delayed, conditional), transformers.models.sew_d.modeling_sew_d (delayed)
missing module named onnxruntime - imported by transformers.onnx.convert (delayed, optional)
missing module named optimum - imported by transformers.cache_utils (delayed, conditional)
missing module named hqq - imported by transformers.cache_utils (conditional)
missing module named transformers.models.timm_wrapper.TimmWrapperImageProcessor - imported by transformers.models.timm_wrapper (conditional, optional), transformers (conditional, optional)
missing module named transformers.models.timm_wrapper.processing_timm_wrapper - imported by transformers.models.timm_wrapper (conditional)
missing module named 'timm.data' - imported by transformers.models.timm_wrapper.configuration_timm_wrapper (conditional)
missing module named yt_dlp - imported by transformers.video_utils (delayed, conditional)
missing module named decord - imported by transformers.video_utils (delayed)
missing module named torchvision - imported by transformers.image_utils (conditional), transformers.video_utils (conditional)
missing module named tiktoken - imported by transformers.convert_slow_tokenizer (delayed, optional)
missing module named rjieba - imported by transformers.models.roformer.tokenization_roformer (delayed, optional), transformers.models.roformer.tokenization_utils (delayed, optional)
missing module named 'google.protobuf' - imported by transformers.tokenization_utils_base (delayed, conditional), transformers.convert_slow_tokenizer (delayed, conditional), transformers.utils.sentencepiece_model_pb2 (top-level), transformers.utils.sentencepiece_model_pb2_new (top-level)
missing module named sentencepiece - imported by transformers.convert_slow_tokenizer (delayed, conditional), transformers.models.llama.tokenization_llama (top-level), transformers.models.albert.tokenization_albert (top-level), transformers.models.barthez.tokenization_barthez (top-level), transformers.models.bartpho.tokenization_bartpho (top-level), transformers.models.bert_generation.tokenization_bert_generation (top-level), transformers.models.bert_japanese.tokenization_bert_japanese (conditional), transformers.models.big_bird.tokenization_big_bird (top-level), transformers.models.camembert.tokenization_camembert (top-level), transformers.models.code_llama.tokenization_code_llama (top-level), transformers.models.cpm.tokenization_cpm (top-level), transformers.models.deberta_v2.tokenization_deberta_v2 (top-level), transformers.models.deprecated.ernie_m.tokenization_ernie_m (top-level), transformers.models.deprecated.xlm_prophetnet.tokenization_xlm_prophetnet (delayed, optional), transformers.models.fnet.tokenization_fnet (top-level), transformers.models.gemma.tokenization_gemma (top-level), transformers.models.siglip.tokenization_siglip (top-level), transformers.models.gpt_sw3.tokenization_gpt_sw3 (top-level), transformers.models.layoutxlm.tokenization_layoutxlm (top-level), transformers.models.xlm_roberta.tokenization_xlm_roberta (top-level), transformers.models.m2m_100.tokenization_m2m_100 (top-level), transformers.models.marian.tokenization_marian (top-level), transformers.models.mbart.tokenization_mbart (top-level), transformers.models.mbart50.tokenization_mbart50 (top-level), transformers.models.mluke.tokenization_mluke (top-level), transformers.models.t5.tokenization_t5 (top-level), transformers.models.nllb.tokenization_nllb (top-level), transformers.models.pegasus.tokenization_pegasus (top-level), transformers.models.plbart.tokenization_plbart (top-level), transformers.models.reformer.tokenization_reformer (top-level), transformers.models.rembert.tokenization_rembert (top-level), transformers.models.seamless_m4t.tokenization_seamless_m4t (top-level), transformers.models.speech_to_text.tokenization_speech_to_text (top-level), transformers.models.speecht5.tokenization_speecht5 (top-level), transformers.models.udop.tokenization_udop (top-level), transformers.models.xglm.tokenization_xglm (top-level), transformers.models.xlnet.tokenization_xlnet (top-level)
missing module named 'transformers.utils.dummies_sentencepiece_and_tokenizers_objects' - imported by transformers (conditional, optional)
missing module named quark - imported by transformers.utils.quantization_config (delayed, conditional)
missing module named 'compressed_tensors.config' - imported by transformers.utils.quantization_config (delayed)
missing module named compressed_tensors - imported by transformers.utils.quantization_config (delayed, conditional)
missing module named ray - imported by transformers.trainer_utils (delayed), transformers.integrations.integration_utils (delayed)
missing module named 'mlx.core' - imported by transformers.tokenization_utils_base (delayed, conditional)
missing module named 'jinja2.sandbox' - imported by transformers.utils.chat_template_utils (conditional)
missing module named 'jinja2.ext' - imported by transformers.utils.chat_template_utils (conditional)
missing module named librosa - imported by transformers.audio_utils (conditional)
missing module named pyctcdecode - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed, conditional), transformers.pipelines.automatic_speech_recognition (conditional), transformers.pipelines (delayed, conditional, optional)
missing module named kenlm - imported by transformers.pipelines (delayed, conditional, optional)
missing module named pytesseract - imported by transformers.models.layoutlmv2.image_processing_layoutlmv2 (conditional), transformers.models.layoutlmv3.image_processing_layoutlmv3 (conditional), transformers.pipelines.document_question_answering (conditional)
missing module named torchaudio - imported by transformers.models.audio_spectrogram_transformer.feature_extraction_audio_spectrogram_transformer (conditional), transformers.models.granite_speech.feature_extraction_granite_speech (conditional), transformers.pipelines.audio_classification (delayed, conditional), transformers.pipelines.automatic_speech_recognition (delayed, conditional)
missing module named causal_conv1d - imported by transformers.models.jamba.modeling_jamba (conditional), transformers.models.bamba.modeling_bamba (conditional), transformers.models.falcon_mamba.modeling_falcon_mamba (conditional), transformers.models.granitemoehybrid.modeling_granitemoehybrid (conditional), transformers.models.mamba.modeling_mamba (conditional), transformers.models.mamba2.modeling_mamba2 (conditional), transformers.models.zamba.modeling_zamba (conditional), transformers.models.zamba2.modeling_zamba2 (conditional)
missing module named 'mamba_ssm.ops' - imported by transformers.models.jamba.modeling_jamba (conditional), transformers.models.bamba.modeling_bamba (conditional), transformers.models.falcon_mamba.modeling_falcon_mamba (conditional), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (conditional), transformers.models.granitemoehybrid.modeling_granitemoehybrid (conditional), transformers.models.mamba.modeling_mamba (conditional), transformers.models.mamba2.modeling_mamba2 (conditional), transformers.models.zamba.modeling_zamba (conditional), transformers.models.zamba2.modeling_zamba2 (conditional)
missing module named fast_lsh_cumulation - imported by transformers.models.yoso.modeling_yoso (delayed)
missing module named pycocotools - imported by transformers.models.conditional_detr.image_processing_conditional_detr (delayed, optional), transformers.models.conditional_detr.image_processing_conditional_detr_fast (delayed, optional), transformers.models.deformable_detr.image_processing_deformable_detr (delayed, optional), transformers.models.deformable_detr.image_processing_deformable_detr_fast (delayed, optional), transformers.models.deprecated.deta.image_processing_deta (delayed, optional), transformers.models.detr.image_processing_detr (delayed, optional), transformers.models.detr.image_processing_detr_fast (delayed, optional), transformers.models.grounding_dino.image_processing_grounding_dino (delayed, optional), transformers.models.grounding_dino.image_processing_grounding_dino_fast (delayed, optional), transformers.models.yolos.image_processing_yolos (delayed, optional), transformers.models.yolos.image_processing_yolos_fast (delayed, optional)
missing module named 'torchvision.io' - imported by transformers.models.conditional_detr.image_processing_conditional_detr_fast (conditional), transformers.models.deformable_detr.image_processing_deformable_detr_fast (conditional), transformers.models.detr.image_processing_detr_fast (conditional), transformers.models.grounding_dino.image_processing_grounding_dino_fast (conditional), transformers.models.yolos.image_processing_yolos_fast (conditional)
missing module named 'scipy.stats' - imported by transformers.data.metrics (conditional), transformers.models.conditional_detr.image_processing_conditional_detr (conditional), transformers.models.deformable_detr.image_processing_deformable_detr (conditional), transformers.models.detr.image_processing_detr (conditional), transformers.models.esm.modeling_esmfold (delayed, conditional), transformers.models.grounding_dino.image_processing_grounding_dino (conditional), transformers.models.yolos.image_processing_yolos (conditional)
missing module named 'scipy.special' - imported by transformers.models.conditional_detr.image_processing_conditional_detr (conditional), transformers.models.deformable_detr.image_processing_deformable_detr (conditional), transformers.models.detr.image_processing_detr (conditional), transformers.models.grounding_dino.image_processing_grounding_dino (conditional), transformers.models.yolos.image_processing_yolos (conditional)
missing module named jieba - imported by transformers.models.cpm.tokenization_cpm (delayed, optional), transformers.models.cpm.tokenization_cpm_fast (delayed, optional), transformers.models.cpmant.tokenization_cpmant (conditional), transformers.models.xlm.tokenization_xlm (delayed, conditional, optional)
missing module named pythainlp - imported by transformers.models.xlm.tokenization_xlm (delayed, conditional, optional)
missing module named Mykytea - imported by transformers.models.flaubert.tokenization_flaubert (delayed, conditional, optional), transformers.models.herbert.tokenization_herbert (delayed, conditional, optional), transformers.models.xlm.tokenization_xlm (delayed, conditional, optional)
missing module named sacremoses - imported by transformers.models.biogpt.tokenization_biogpt (delayed, optional), transformers.models.deprecated.transfo_xl.tokenization_transfo_xl (conditional), transformers.models.flaubert.tokenization_flaubert (delayed, optional), transformers.models.fsmt.tokenization_fsmt (delayed, optional), transformers.models.herbert.tokenization_herbert (delayed, optional), transformers.models.marian.tokenization_marian (delayed, optional), transformers.models.xlm.tokenization_xlm (delayed, optional)
missing module named regex.DEFAULT_VERSION - imported by regex (delayed, optional), regex.regex (delayed, optional)
missing module named 'jax.experimental' - imported by transformers.generation.flax_logits_process (top-level)
missing module named 'jax.lax' - imported by transformers.generation.flax_logits_process (top-level)
missing module named 'peft.tuners' - imported by transformers.integrations.peft (delayed), transformers.models.data2vec.modeling_data2vec_audio (delayed, conditional), transformers.models.unispeech_sat.modeling_unispeech_sat (delayed, conditional), transformers.models.wav2vec2.modeling_wav2vec2 (delayed, conditional), transformers.models.wav2vec2_bert.modeling_wav2vec2_bert (delayed, conditional), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (delayed, conditional), transformers.models.wavlm.modeling_wavlm (delayed, conditional)
missing module named 'pyctcdecode.constants' - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed)
missing module named 'pyctcdecode.alphabet' - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed)
missing module named 'phonemizer.separator' - imported by transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (delayed)
missing module named 'phonemizer.backend' - imported by transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (delayed)
missing module named uroman - imported by transformers.models.vits.tokenization_vits (conditional)
missing module named phonemizer - imported by transformers.models.vits.tokenization_vits (conditional)
missing module named 'scipy.ndimage' - imported by transformers.models.vitpose.image_processing_vitpose (conditional)
missing module named 'scipy.linalg' - imported by transformers.models.vitpose.image_processing_vitpose (conditional)
missing module named tensorflow_probability - imported by transformers.models.groupvit.modeling_tf_groupvit (conditional, optional), transformers.models.tapas.modeling_tf_tapas (conditional, optional)
missing module named 'tensorflow.compiler' - imported by transformers.generation.tf_utils (top-level), transformers.models.t5.modeling_tf_t5 (top-level)
missing module named 'apex.normalization' - imported by transformers.models.longt5.modeling_longt5 (optional), transformers.models.t5.modeling_t5 (optional), transformers.models.pix2struct.modeling_pix2struct (optional), transformers.models.pop2piano.modeling_pop2piano (optional)
missing module named 'torchaudio.compliance' - imported by transformers.models.speech_to_text.feature_extraction_speech_to_text (conditional)
missing module named num2words - imported by transformers.models.smolvlm.processing_smolvlm (conditional)
missing module named 'tensorflow.experimental' - imported by transformers.models.sam.image_processing_sam (conditional)
missing module named 'torchvision.ops' - imported by transformers.models.deprecated.deta.image_processing_deta (conditional), transformers.models.deprecated.deta.modeling_deta (conditional), transformers.models.omdet_turbo.processing_omdet_turbo (conditional), transformers.models.sam.image_processing_sam (conditional)
missing module named 'torch.autograd' - imported by transformers.models.deprecated.deta.modeling_deta (top-level), transformers.models.ibert.quant_modules (top-level), transformers.models.reformer.modeling_reformer (top-level)
missing module named faiss - imported by transformers.models.rag.retrieval_rag (conditional)
missing module named spacy - imported by transformers.models.openai.tokenization_openai (delayed, optional)
missing module named ftfy - imported by transformers.models.clip.tokenization_clip (delayed, optional), transformers.models.openai.tokenization_openai (delayed, optional)
missing module named 'torch.cuda' - imported by transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.oneformer.modeling_oneformer (top-level)
missing module named nltk - imported by transformers.models.nougat.tokenization_nougat_fast (conditional)
missing module named Levenshtein - imported by transformers.models.nougat.tokenization_nougat_fast (conditional)
missing module named 'flash_attn.ops' - imported by transformers.models.modernbert.modeling_modernbert (conditional)
missing module named 'flash_attn.flash_attn_interface' - imported by transformers.models.modernbert.modeling_modernbert (conditional)
missing module named transformers.models.mistral3.processing_mistral3 - imported by transformers.models.mistral3 (conditional)
missing module named bs4 - imported by transformers.models.markuplm.feature_extraction_markuplm (conditional)
missing module named 'mambapy.pscan' - imported by transformers.models.mamba.modeling_mamba (conditional)
missing module named 'detectron2.modeling' - imported by transformers.models.layoutlmv2.modeling_layoutlmv2 (conditional)
missing module named detectron2 - imported by transformers.models.layoutlmv2.configuration_layoutlmv2 (conditional), transformers.models.layoutlmv2.modeling_layoutlmv2 (conditional)
missing module named sklearn - imported by transformers.generation.candidate_generator (conditional)
missing module named 'torch.fx' - imported by transformers.utils.import_utils (delayed, conditional), transformers.models.gptj.modeling_gptj (top-level)
missing module named keras_nlp - imported by transformers.models.gpt2.tokenization_gpt2_tf (conditional)
missing module named transformers.models.funnel.convert_funnel_original_tf_checkpoint_to_pytorch - imported by transformers.models.funnel (conditional)
missing module named g2p_en - imported by transformers.models.fastspeech2_conformer.tokenization_fastspeech2_conformer (delayed, optional)
missing module named selective_scan_cuda - imported by transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level)
missing module named mamba_ssm - imported by transformers.utils.import_utils (delayed, conditional), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level)
missing module named causal_conv1d_cuda - imported by transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (optional)
missing module named mambapy - imported by transformers.models.falcon_mamba.modeling_falcon_mamba (conditional)
missing module named 'torch.types' - imported by transformers.models.esm.openfold_utils.tensor_utils (top-level), transformers.models.esm.openfold_utils.feats (top-level)
missing module named 'natten.functional' - imported by transformers.models.dinat.modeling_dinat (conditional)
missing module named scann - imported by transformers.models.deprecated.realm.retrieval_realm (delayed)
missing module named 'tensorflow.compat' - imported by transformers.models.bert_generation.modeling_bert_generation (delayed, optional), transformers.models.deprecated.realm.retrieval_realm (delayed)
missing module named 'pytorch_quantization.nn' - imported by transformers.models.deprecated.qdqbert.modeling_qdqbert (conditional, optional)
missing module named pytorch_quantization - imported by transformers.models.deprecated.qdqbert.modeling_qdqbert (conditional, optional)
missing module named xformers - imported by transformers.models.deprecated.open_llama.modeling_open_llama (optional)
missing module named natten - imported by transformers.models.deprecated.nat.modeling_nat (conditional)
missing module named soundfile - imported by transformers.models.csm.processing_csm (conditional)
missing module named emoji - imported by transformers.models.bertweet.tokenization_bertweet (delayed, optional)
missing module named rhoknp - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional)
missing module named sudachipy - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional)
missing module named unidic - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named unidic_lite - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named ipadic - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named fugashi - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional)
missing module named tensorflow_hub - imported by transformers.models.bert_generation.modeling_bert_generation (delayed, optional)
missing module named transformers.models.bamba.processing_bamba - imported by transformers.models.bamba (conditional)
missing module named spqr_quant - imported by transformers.integrations.spqr (delayed, conditional)
missing module named swanlab - imported by transformers.integrations.integration_utils (delayed)
missing module named 'dvclive.utils' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named 'dvclive.plots' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named dvclive - imported by transformers.integrations.integration_utils (delayed)
missing module named flytekitplugins - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named flytekit - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named clearml - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named codecarbon - imported by transformers.integrations.integration_utils (delayed)
missing module named 'neptune.utils' - imported by transformers.integrations.integration_utils (delayed)
missing module named 'neptune.exceptions' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named 'neptune.new' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named 'neptune.internal' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named neptune - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named dagshub - imported by transformers.integrations.integration_utils (delayed)
missing module named mlflow - imported by transformers.integrations.integration_utils (delayed)
missing module named azureml - imported by transformers.integrations.integration_utils (delayed)
missing module named 'wandb.sdk' - imported by transformers.integrations.integration_utils (delayed)
missing module named 'wandb.env' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named sigopt - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named 'datasets.load' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named 'ray.tune' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named comet_ml - imported by transformers.integrations.integration_utils (delayed, conditional, optional)
missing module named fast_hadamard_transform - imported by transformers.integrations.higgs (conditional)
missing module named 'flute.tune' - imported by transformers.integrations.higgs (conditional)
missing module named 'flute.integrations' - imported by transformers.integrations.higgs (conditional)
missing module named fbgemm_gpu - imported by transformers.integrations.fbgemm_fp8 (conditional)
missing module named 'awq.quantize' - imported by transformers.integrations.awq (delayed, conditional)
missing module named 'awq.modules' - imported by transformers.integrations.awq (delayed, conditional)
missing module named awq - imported by transformers.integrations.awq (delayed)
missing module named aqlm - imported by transformers.integrations.aqlm (delayed)
missing module named 'sklearn.metrics' - imported by transformers.data.metrics (conditional)
missing module named 'torch._dynamo' - imported by transformers.utils.import_utils (delayed, optional)
missing module named 'habana_frameworks.torch' - imported by transformers.utils.import_utils (delayed)
missing module named habana_frameworks - imported by transformers.utils.import_utils (delayed, conditional)
missing module named torch_musa - imported by transformers.utils.import_utils (delayed)
missing module named torch_mlu - imported by transformers.utils.import_utils (delayed)
missing module named transformers.TFVisionEncoderDecoderModel - imported by transformers (delayed, conditional), transformers.models.vision_encoder_decoder.modeling_vision_encoder_decoder (delayed, conditional)
missing module named transformers.UdopConfig - imported by transformers (top-level), transformers.models.udop.modeling_udop (top-level)
missing module named transformers.RegNetConfig - imported by transformers (top-level), transformers.models.regnet.modeling_flax_regnet (top-level)
missing module named transformers.MaskFormerForInstanceSegmentationOutput - imported by transformers (conditional), transformers.models.maskformer.image_processing_maskformer (conditional)
missing module named transformers.AutoModelForImageTextToText - imported by transformers (top-level), transformers.models.colpali.modeling_colpali (top-level)
missing module named transformers.AutoTokenizer - imported by transformers (conditional), transformers.onnx.utils (delayed, conditional), transformers.models.deprecated.realm.retrieval_realm (top-level), transformers.models.mgp_str.processing_mgp_str (top-level)
missing module named transformers.AutoProcessor - imported by transformers (conditional), transformers.onnx.utils (delayed, conditional)
missing module named transformers.AutoFeatureExtractor - imported by transformers (conditional), transformers.onnx.utils (delayed, conditional)
missing module named transformers.TFEncoderDecoderModel - imported by transformers (delayed, conditional), transformers.models.encoder_decoder.modeling_encoder_decoder (delayed, conditional)
missing module named transformers.AutoConfig - imported by transformers (delayed), transformers.utils.backbone_utils (delayed)
missing module named transformers.AutoBackbone - imported by transformers (delayed), transformers.utils.backbone_utils (delayed)
missing module named curio - imported by sniffio._impl (delayed, conditional)
missing module named 'trio.testing' - imported by anyio._backends._trio (delayed)
missing module named exceptiongroup - imported by anyio._core._exceptions (conditional), anyio._core._sockets (conditional), anyio._backends._asyncio (conditional), anyio._backends._trio (conditional)
missing module named 'trio.to_thread' - imported by anyio._backends._trio (top-level)
missing module named 'trio.socket' - imported by anyio._backends._trio (top-level)
missing module named outcome - imported by anyio._backends._trio (top-level)
missing module named 'trio.lowlevel' - imported by anyio._backends._trio (top-level)
missing module named 'trio.from_thread' - imported by anyio._backends._trio (top-level)
missing module named _pytest - imported by anyio._backends._asyncio (delayed)
missing module named uvloop - imported by anyio._backends._asyncio (delayed, conditional)
missing module named trio - imported by httpx._transports.asgi (delayed, conditional), httpcore._synchronization (optional), httpcore._backends.trio (top-level), openai.resources.vector_stores.file_batches (delayed, conditional)
missing module named sounddevice - imported by openai._extras.sounddevice_proxy (delayed, conditional, optional)
missing module named 'websockets.exceptions' - imported by openai.resources.beta.realtime.realtime (delayed)
missing module named 'websockets.asyncio' - imported by openai.resources.beta.realtime.realtime (delayed, conditional, optional)
missing module named 'websockets.sync' - imported by openai.resources.beta.realtime.realtime (delayed, conditional, optional)
missing module named 'websockets.extensions' - imported by openai.types.websocket_connection_options (conditional)
missing module named websockets - imported by openai.types.websocket_connection_options (conditional)
missing module named jiter.from_json - imported by jiter (top-level), openai.lib.streaming.chat._completions (top-level)
missing module named socksio - imported by httpcore._sync.socks_proxy (top-level), httpcore._async.socks_proxy (top-level), httpx._transports.default (delayed, conditional, optional)
missing module named 'h2.settings' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.exceptions' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named h2 - imported by httpcore._sync.http2 (top-level), httpx._client (delayed, conditional, optional)
missing module named 'rich.table' - imported by httpx._main (top-level)
missing module named 'rich.syntax' - imported by httpx._main (top-level)
missing module named 'rich.progress' - imported by httpx._main (top-level)
missing module named 'rich.markup' - imported by httpx._main (top-level)
missing module named 'rich.console' - imported by httpx._main (top-level)
missing module named 'pygments.util' - imported by httpx._main (top-level)
missing module named pygments - imported by httpx._main (top-level)
missing module named '_typeshed.wsgi' - imported by httpx._transports.wsgi (conditional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named PIL._avif - imported by PIL (optional), PIL.AvifImagePlugin (optional)
missing module named defusedxml - imported by PIL.Image (optional)
