{"app": {"name": "TikTok Automation", "version": "1.0.0"}, "ai": {"story_generation": {"provider": "openai", "model": "gpt-3.5-turbo", "temperature": 0.7, "max_tokens": 1000}}, "tts": {"provider": "tts", "voice_options": {"male": ["male1", "male2"], "female": ["female1", "female2"], "ai": ["ai1", "ai2"]}}, "image": {"provider": "stable_diffusion", "resolution": {"width": 1080, "height": 1920}}, "video": {"format": "mp4", "resolution": {"width": 1080, "height": 1920}, "fps": 30, "effects": {"enabled": true, "default": "zoom"}, "subtitles": {"enabled": true, "font": "<PERSON><PERSON>", "size": 40, "color": "white", "stroke_color": "black", "stroke_width": 2}}, "export": {"output_dir": "output", "metadata": {"enabled": true, "format": "json"}}, "api_keys": {"openai": "", "stable_diffusion": ""}}